import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Container,
  Paper,
  Button,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
  Stack,
  TextField,
  IconButton,
  Menu,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  CircularProgress,
  Badge,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ShareIcon from '@mui/icons-material/Share';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Grid from '@mui/material/Unstable_Grid2';
import { Map<PERSON>ontainer, TileLayer, <PERSON>er, Popup, Polyline } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import axios from 'axios';
import L from 'leaflet';
import { useNavigate } from 'react-router-dom';

// Enhanced components
import EnhancedLayout from '../components/EnhancedLayout';
import StopManagementPanel from '../components/StopManagementPanel';
import AmenityDiscovery from '../components/AmenityDiscovery';
import InteractiveMap from '../components/InteractiveMap';
import LocationSearch from '../components/LocationSearch';
import { ThemeProvider } from '../theme/ThemeProvider';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import DirectionsBikeIcon from '@mui/icons-material/DirectionsBike';
import DirectionsWalkIcon from '@mui/icons-material/DirectionsWalk';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import TurnLeftIcon from '@mui/icons-material/TurnLeft';
import TurnRightIcon from '@mui/icons-material/TurnRight';
import StraightIcon from '@mui/icons-material/Straight';
import WbSunnyIcon from '@mui/icons-material/WbSunny';
import CloudIcon from '@mui/icons-material/Cloud';
import UmbrellaIcon from '@mui/icons-material/BeachAccess';
import AcUnitIcon from '@mui/icons-material/AcUnit';
import FilterDramaIcon from '@mui/icons-material/FilterDrama';
import SpeedIcon from '@mui/icons-material/Speed';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SearchIcon from '@mui/icons-material/Search';
import MyLocationIcon from '@mui/icons-material/MyLocation';
import AddLocationIcon from '@mui/icons-material/AddLocation';
import PlaceIcon from '@mui/icons-material/Place';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import SchoolIcon from '@mui/icons-material/School';
import HotelIcon from '@mui/icons-material/Hotel';
import LocalGasStationIcon from '@mui/icons-material/LocalGasStation';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearIcon from '@mui/icons-material/Clear';
import SaveIcon from '@mui/icons-material/Save';
import EditIcon from '@mui/icons-material/Edit';
import { useAuth } from '../contexts/AuthContext';
import { alpha } from '@mui/material/styles';
import InteractiveMap from '../components/InteractiveMap';
import LocationSearch from '../components/LocationSearch';


// Fix for default marker icon
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

// Create custom markers for start and end points
const startIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const endIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Custom marker icons for nearby search
const hotelIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-violet.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});
const restaurantIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-orange.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});
const placeIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-blue.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// API base URL - use environment variable or fallback to localhost for development
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
});

const vehicleIcons = {
  car: <DirectionsCarIcon />,
  bike: <DirectionsBikeIcon />,
  walk: <DirectionsWalkIcon />,
};

const weatherIcons = {
  sunny: <WbSunnyIcon style={{ color: '#FFB900' }} />,
  cloudy: <CloudIcon style={{ color: '#757575' }} />,
  rainy: <UmbrellaIcon style={{ color: '#0078D7' }} />,
  snowy: <AcUnitIcon style={{ color: '#00B7C3' }} />,
  foggy: <FilterDramaIcon style={{ color: '#9E9E9E' }} />,
};

const trafficIcons = {
  light: <CheckCircleIcon style={{ color: '#107C10' }} />,
  moderate: <SpeedIcon style={{ color: '#FFB900' }} />,
  heavy: <WarningIcon style={{ color: '#E81123' }} />,
};

const getDirectionIcon = (instruction) => {
  if (instruction.includes('left')) {
    return <TurnLeftIcon color="primary" />;
  } else if (instruction.includes('right')) {
    return <TurnRightIcon color="primary" />;
  } else if (instruction.includes('straight')) {
    return <StraightIcon color="primary" />;
  } else {
    return <ArrowRightAltIcon color="primary" />;
  }
};

const formatDistance = (meters) => {
  if (meters < 1000) {
    return `${Math.round(meters)} m`;
  } else {
    return `${(meters / 1000).toFixed(1)} km`;
  }
};

const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  if (minutes < 1) {
    return `${Math.round(seconds)} sec`;
  } else {
    return `${minutes} min`;
  }
};

const getPolylineColor = (vehicleType, routeIndex) => {
  // Base colors for each vehicle type
  const baseColors = {
    car: '#1976d2',
    bike: '#2e7d32',
    walk: '#ed6c02'
  };

  // Variations for multiple routes of the same vehicle type
  const variations = [
    '', // No variation for first route
    '99', // Lighter
    '66' // Even lighter
  ];

  return baseColors[vehicleType] + variations[routeIndex] || baseColors[vehicleType];
};

// Assign color based on traffic for each route
const getTrafficColor = (traffic, idx) => {
  if (traffic === 'heavy') return '#e53935'; // Red
  if (traffic === 'moderate') return '#fbc02d'; // Yellow
  if (traffic === 'light') return '#1976d2'; // Blue
  // Fallback: cycle colors if more than 3
  const palette = ['#1976d2', '#fbc02d', '#e53935'];
  return palette[idx % 3];
};

const getWeatherDescription = (weather) => {
  if (!weather) return '';

  const descriptions = {
    sunny: `Sunny, ${weather.temperature}°C`,
    cloudy: `Cloudy, ${weather.temperature}°C`,
    rainy: `Rainy, ${weather.temperature}°C, ${weather.precipitation}% precipitation`,
    snowy: `Snowy, ${weather.temperature}°C, ${weather.precipitation}% precipitation`,
    foggy: `Foggy, ${weather.temperature}°C`
  };

  return descriptions[weather.condition] || '';
};

const getTrafficDescription = (traffic) => {
  const descriptions = {
    light: 'Light traffic, good road conditions',
    moderate: 'Moderate traffic, expect minor delays',
    heavy: 'Heavy traffic, significant delays expected'
  };

  return descriptions[traffic] || '';
};

const FindRoute = () => {
  const [startQuery, setStartQuery] = useState("");
  const [endQuery, setEndQuery] = useState("");
  const [startOptions, setStartOptions] = useState([]);
  const [endOptions, setEndOptions] = useState([]);
  const [startCoords, setStartCoords] = useState(null); // {lat, lng}
  const [endCoords, setEndCoords] = useState(null); // {lat, lng}
  const [nearbyType, setNearbyType] = useState("");
  const [nearbyResults, setNearbyResults] = useState([]);
  const [vehicleType, setVehicleType] = useState('car');
  const [userWeather, setUserWeather] = useState('');
  const [route, setRoute] = useState(null);
  const [routePaths, setRoutePaths] = useState([]);
  const [selectedRouteOption, setSelectedRouteOption] = useState('Route 1: Fast (Shortest)');
  const [error, setError] = useState('');
  const [mapCenter, setMapCenter] = useState([30.3165, 78.0322]);
  const [mapZoom, setMapZoom] = useState(13);
  const [steps, setSteps] = useState([]);
  const [userCoords, setUserCoords] = useState(null); // {lat, lng}
  const [watchId, setWatchId] = useState(null);

  // Enhanced features state
  const [stops, setStops] = useState([]);
  const [amenities, setAmenities] = useState([]);
  const [selectedAmenityTypes, setSelectedAmenityTypes] = useState([]);
  const [selectedStopId, setSelectedStopId] = useState(null);
  const [routeAlternatives, setRouteAlternatives] = useState([]);
  const [isCalculatingRoute, setIsCalculatingRoute] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [customPins, setCustomPins] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [optimizedRoute, setOptimizedRoute] = useState(null);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [shareType, setShareType] = useState('google'); // 'google' or 'apple'
  const navigate = useNavigate();
  const { isAuthenticated, api } = useAuth();
  const [landmarks, setLandmarks] = useState([]);
  const [googleMapsUrl, setGoogleMapsUrl] = useState('');

  // Add prototype route state
  const [prototypeRoute, setPrototypeRoute] = useState(null);
  const [directRoute, setDirectRoute] = useState(null); // Direct/greedy

  // Enhanced map features state
  const [customPins, setCustomPins] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [prototypeRouteLoading, setPrototypeRouteLoading] = useState(false);
  const [prototypeRouteError, setPrototypeRouteError] = useState('');

  // Fetch Dehradun landmarks from backend on mount
  useEffect(() => {
    api.get('/locations').then(res => {
      setLandmarks(res.data);
    });
  }, []);

  // OSM Nominatim search for start
  useEffect(() => {
    if (startQuery.length < 3) return;
    const fetchOptions = async () => {
      const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(startQuery)}&addressdetails=1&limit=5`;
      const res = await fetch(url);
      const data = await res.json();
      setStartOptions(data);
    };
    fetchOptions();
  }, [startQuery]);

  // OSM Nominatim search for end
  useEffect(() => {
    if (endQuery.length < 3) return;
    const fetchOptions = async () => {
      const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(endQuery)}&addressdetails=1&limit=5`;
      const res = await fetch(url);
      const data = await res.json();
      setEndOptions(data);
    };
    fetchOptions();
  }, [endQuery]);

  // OSM Overpass API for nearby search
  const handleNearbySearch = async () => {
    if (!startCoords && !userCoords) {
      setError("Set a start location or use your current location first.");
      return;
    }
    const { lat, lng } = startCoords || userCoords;
    const radius = 2; // km
    const filtered = landmarks.filter(l => {
      const dLat = (l.lat - lat) * Math.PI / 180;
      const dLng = (l.lng - lng) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) + Math.cos(lat * Math.PI / 180) * Math.cos(l.lat * Math.PI / 180) * Math.sin(dLng/2) * Math.sin(dLng/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      const dist = 6371 * c;
      return dist <= radius;
    });
    setNearbyResults(filtered);
  };

  // Haversine formula for distance in meters
  const haversine = (lat1, lon1, lat2, lon2) => {
    const toRad = x => x * Math.PI / 180;
    const R = 6371e3;
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Use current location button handler
  const handleUseCurrentLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser.');
      return;
    }
    navigator.geolocation.getCurrentPosition(
      pos => {
        const { latitude, longitude } = pos.coords;
        setUserCoords({ lat: latitude, lng: longitude });
      },
      err => setError('Failed to get current location.'),
      { enableHighAccuracy: true }
    );
  };

  // Track user movement and update live distance
  useEffect(() => {
    if (!route || !route.end) return;
    if (!userCoords) return;
    if (watchId) navigator.geolocation.clearWatch(watchId);
    const id = navigator.geolocation.watchPosition(
      pos => {
        const { latitude, longitude } = pos.coords;
        setUserCoords({ lat: latitude, lng: longitude });
        // Calculate distance to destination
        setLiveDistance(haversine(latitude, longitude, route.end.lat, route.end.lng));
      },
      err => {},
      { enableHighAccuracy: true, maximumAge: 10000, timeout: 20000 }
    );
    setWatchId(id);
    return () => { if (id) navigator.geolocation.clearWatch(id); };
  }, [route, userCoords]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setRoutePaths([]);
    setSteps([]);

    try {
      if (!isAuthenticated) {
        navigate('/login');
        return;
      }

      const response = await api.post(
        '/routes',
        {
          start_coords: startCoords,
          end_coords: endCoords,
          vehicle_type: vehicleType,
          route_option: selectedRouteOption,
          user_weather: userWeather || undefined
        }
      );

      setRoute(response.data);

      if (response.data.route_options && response.data.route_options.length > 0) {
        // Set route paths for all options with color and selection info
        setRoutePaths(
          response.data.route_options.map((option, idx) => ({
            path: option.path,
            color: getPolylineColor(response.data.vehicle_type, idx),
            selected: option.option_name === selectedRouteOption
          }))
        );

        // Set steps for selected route option
        const selectedOption = response.data.route_options.find(
          option => option.option_name === selectedRouteOption
        ) || response.data.route_options[0];

        // Update selected route option if it's not in the new options
        if (!response.data.route_options.some(opt => opt.option_name === selectedRouteOption)) {
          setSelectedRouteOption(response.data.route_options[0].option_name);
        }

        if (selectedOption.steps && selectedOption.steps.length > 0) {
          setSteps(selectedOption.steps);
        }

        // Calculate bounds to fit the routes
        const allPoints = response.data.route_options.flatMap(option => option.path);
        if (allPoints.length > 0) {
          const bounds = L.latLngBounds(allPoints);
          const center = bounds.getCenter();
          setMapCenter([center.lat, center.lng]);
          setMapZoom(12);
        }
      }
    } catch (err) {
      if (err.response?.status === 401) {
        navigate('/login');
      } else if (err.response?.status === 422) {
        setError('Please select both start and end locations');
      } else {
        setError('Failed to calculate route');
        console.error('Route error:', err.response?.data || err);
      }
    }
  };

  const handleRouteOptionChange = (e) => {
    setSelectedRouteOption(e.target.value);

    // Update steps and routePaths based on selected route
    if (route && route.route_options) {
      const selectedOption = route.route_options.find(
        option => option.option_name === e.target.value
      );

      if (selectedOption && selectedOption.steps) {
        setSteps(selectedOption.steps);
      } else {
        setSteps([]);
      }

      // Update routePaths to highlight the selected route
      setRoutePaths(
        route.route_options.map((option, idx) => ({
          path: option.path,
          color: getPolylineColor(route.vehicle_type, idx),
          selected: option.option_name === e.target.value
        }))
      );
    }
  };

  // Handle drag end for stops
  const handleDragEnd = (result) => {
    if (!result.destination) return;
    const reordered = Array.from(stops);
    const [removed] = reordered.splice(result.source.index, 1);
    reordered.splice(result.destination.index, 0, removed);
    setStops(reordered);
  };

  // Add a new stop
  const handleAddStop = () => {
    setStops([...stops, { id: Date.now() + Math.random(), lat: null, lng: null, query: '', options: [] }]);
  };

  // Remove a stop
  const handleRemoveStop = (idx) => {
    if (stops.length <= 2) return; // At least 2 stops required
    setStops(stops.filter((_, i) => i !== idx));
  };

  // Update stop query and fetch OSM options
  const handleStopQueryChange = (idx, value) => {
    const newStops = [...stops];
    newStops[idx].query = value;
    setStops(newStops);
    // No OSM fetch needed
  };

  // Set stop coordinates and query from dropdown
  const handleStopSelect = (idx, value) => {
    const newStops = [...stops];
    const found = landmarks.find(l => l.name === value);
    if (found) {
      newStops[idx].lat = found.lat;
      newStops[idx].lng = found.lng;
      newStops[idx].query = found.name; // Always set query to landmark name
    } else {
      newStops[idx].lat = null;
      newStops[idx].lng = null;
      newStops[idx].query = value;
    }
    setStops(newStops);
  };

  // Add a toggle for Google Maps optimization
  const [gmOptimize, setGmOptimize] = useState(true);

  // Optimize multi-stop route (redirect to Google Maps, pin stops, show optimal path)
  const handleOptimizeRoute = async () => {
    setError('');
    setOptimizedRoute(null);
    const validStops = stops.filter(s => s.lat && s.lng);
    if (validStops.length < 2) {
      setError('Please set at least 2 valid stops.');
      return;
    }
    const base = 'https://www.google.com/maps/dir/';
    const waypoints = validStops.map(s => `${s.lat},${s.lng}`).join('/');
    // Redirect to Google Maps in a new tab
    window.open(base + waypoints, '_blank');
  };

  // Google Maps Embed API key (replace with your own key)
  const GOOGLE_MAPS_EMBED_API_KEY = 'YOUR_GOOGLE_MAPS_EMBED_API_KEY'; // TODO: Set your API key here

  // Helper to build Google Maps embed URL (supports up to 10 stops)
  function getGoogleMapsEmbedUrl(stops) {
    if (!GOOGLE_MAPS_EMBED_API_KEY) return '';
    const validStops = stops.filter(s => s.lat && s.lng);
    if (validStops.length < 2) return '';
    const origin = `${validStops[0].lat},${validStops[0].lng}`;
    const destination = `${validStops[validStops.length - 1].lat},${validStops[validStops.length - 1].lng}`;
    const waypoints = validStops.slice(1, -1).map(s => `${s.lat},${s.lng}`).join('|');
    let url = `https://www.google.com/maps/embed/v1/directions?key=${GOOGLE_MAPS_EMBED_API_KEY}`;
    url += `&origin=${origin}&destination=${destination}`;
    if (waypoints) url += `&waypoints=${encodeURIComponent(waypoints)}`;
    return url;
  }

  const handleOpenShareDialog = () => {
    const validStops = stops.filter(s => s.lat && s.lng);
    if (validStops.length < 2) {
      setShareUrl('Please set at least 2 valid stops.');
    } else if (validStops.length > 10) {
      setShareUrl('Google Maps embed only supports up to 10 stops.');
    } else {
      const base = 'https://www.google.com/maps/dir/';
      const waypoints = validStops.map(s => `${s.lat},${s.lng}`).join('/');
      setShareUrl(base + waypoints);
    }
    setShareType('google');
    setShareDialogOpen(true);
  };

  const handleCopyShareUrl = () => {
    navigator.clipboard.writeText(shareUrl);
  };

  // 1. Allow clicking any landmark pin to add as a stop
  function handleAddLandmarkAsStop(landmark) {
    // Prevent duplicate stops
    if (stops.some(s => s.lat === landmark.lat && s.lng === landmark.lng)) return;
    setStops([...stops, { id: Date.now() + Math.random(), lat: landmark.lat, lng: landmark.lng, query: landmark.name, options: [] }]);
  }

  // 2. Add a Reset Route button
  function handleResetRoute() {
    setStops([{ id: Date.now() + Math.random(), lat: null, lng: null, query: '', options: [] }]);
    setOptimizedRoute(null);
    setSteps([]);
    setError('');
  }

  // Handler for 'Test Route' button
  const handleTestRoute = async () => {
    setPrototypeRoute(null);
    setDirectRoute(null);
    setPrototypeRouteError('');
    setPrototypeRouteLoading(true);
    const validStops = stops.filter(s => s.lat && s.lng);
    if (validStops.length < 2) {
      setPrototypeRouteError('Please set at least 2 valid stops.');
      setPrototypeRouteLoading(false);
      return;
    }
    try {
      // Always use the actual landmark name from the landmarks list
      const stopNames = validStops.map(s => {
        const found = landmarks.find(l => l.lat === s.lat && l.lng === s.lng);
        return found ? found.name : s.query;
      });
      // 1. Get Floyd-Warshall route
      const fwRes = await api.post('/multi-floyd-warshall', {
        start: stopNames[0],
        destinations: stopNames.slice(1)
      });
      if (fwRes.data && Array.isArray(fwRes.data.path_coords)) {
        setPrototypeRoute(fwRes.data);
      } else {
        setPrototypeRouteError('No Floyd-Warshall route found.');
      }
      // 2. Get direct/greedy route (assume backend endpoint exists: /multi-direct-route)
      try {
        const directRes = await api.post('/multi-direct-route', {
          start: stopNames[0],
          destinations: stopNames.slice(1)
        });
        if (directRes.data && Array.isArray(directRes.data.path_coords)) {
          setDirectRoute(directRes.data);
        }
      } catch (e) {
        // If endpoint not available, skip
      }
    } catch (e) {
      setPrototypeRouteError('Failed to get prototype route.');
    }
    setPrototypeRouteLoading(false);
  };

  // Fix Droppable defaultProps warning by using default parameters
  const DroppableWrapper = (props) => {
    const { children, ...rest } = props;
    return <Droppable {...rest}>{children}</Droppable>;
  };

  // Enhanced map features handlers
  const handlePinAdd = (pin) => {
    setCustomPins(prev => [...prev, pin]);
  };

  const handlePinUpdate = (updatedPin) => {
    setCustomPins(prev => prev.map(pin =>
      pin.id === updatedPin.id ? updatedPin : pin
    ));
  };

  const handlePinDelete = (pinToDelete) => {
    setCustomPins(prev => prev.filter(pin => pin.id !== pinToDelete.id));
  };

  const handleLocationSelect = (location) => {
    // Add logic to use selected location as start or end point
    if (!startCoords) {
      setStartCoords({ lat: location.lat, lng: location.lng });
      setStartQuery(location.name);
    } else if (!endCoords) {
      setEndCoords({ lat: location.lat, lng: location.lng });
      setEndQuery(location.name);
    } else {
      // If both are set, replace end location
      setEndCoords({ lat: location.lat, lng: location.lng });
      setEndQuery(location.name);
    }
  };

  const handleSearchResults = (results) => {
    setSearchResults(results);
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
  };

  // Enhanced stop management handlers
  const handleStopsChange = useCallback((newStops) => {
    setStops(newStops);
  }, []);

  const handleStopAdd = useCallback((stop) => {
    const newStop = {
      ...stop,
      id: stop.id || `stop_${Date.now()}_${Math.random()}`,
      order: stops.length
    };
    setStops(prev => [...prev, newStop]);
  }, [stops.length]);

  const handleStopEdit = useCallback((stop) => {
    setStops(prev => prev.map(s => s.id === stop.id ? stop : s));
  }, []);

  const handleStopDelete = useCallback((stopId) => {
    setStops(prev => prev.filter(s => s.id !== stopId)
      .map((s, index) => ({ ...s, order: index })));
  }, []);

  const handleStopSelect = useCallback((stop) => {
    setSelectedStopId(stop.id);
  }, []);

  const handleOptimizeRoute = useCallback(async () => {
    if (stops.length < 3) return;

    setIsCalculatingRoute(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        'http://localhost:8000/routes/optimize-stops',
        {
          stops: stops,
          optimization_type: 'fastest',
          include_amenities: selectedAmenityTypes.length > 0,
          amenity_types: selectedAmenityTypes
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      setStops(response.data.stops);
      setOptimizedRoute(response.data);
      if (response.data.amenities) {
        setAmenities(response.data.amenities);
      }
    } catch (error) {
      console.error('Route optimization failed:', error);
      setError('Failed to optimize route. Please try again.');
    } finally {
      setIsCalculatingRoute(false);
    }
  }, [stops, selectedAmenityTypes]);

  const handleCalculateEnhancedRoute = useCallback(async () => {
    if (stops.length < 2) return;

    setIsCalculatingRoute(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        'http://localhost:8000/routes/calculate-with-stops',
        {
          stops: stops,
          optimize_order: false,
          vehicle_type: vehicleType
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      setOptimizedRoute(response.data);
      setRoutePaths([{
        coordinates: response.data.coordinates,
        color: '#1976d2',
        weight: 5,
        opacity: 0.8
      }]);
    } catch (error) {
      console.error('Route calculation failed:', error);
      setError('Failed to calculate route. Please try again.');
    } finally {
      setIsCalculatingRoute(false);
    }
  }, [stops, vehicleType]);

  // Amenity discovery handlers
  const handleAmenityTypesChange = useCallback((types) => {
    setSelectedAmenityTypes(types);
  }, []);

  const handleAmenityAdd = useCallback((amenity) => {
    const amenityStop = {
      id: `amenity_${amenity.id}`,
      name: amenity.name,
      lat: amenity.lat,
      lng: amenity.lng,
      stop_type: 'amenity',
      category: amenity.category,
      order: stops.length
    };
    setStops(prev => [...prev, amenityStop]);
  }, [stops.length]);

  // Quick action handlers for mobile FAB
  const handleQuickAction = useCallback((action) => {
    switch (action) {
      case 'add-stop':
        // Open coordinate input dialog or trigger map click mode
        break;
      case 'search':
        setActiveTab(1); // Switch to search tab
        break;
      case 'calculate':
        handleCalculateEnhancedRoute();
        break;
      default:
        break;
    }
  }, [handleCalculateEnhancedRoute]);

  if (!isAuthenticated) {
    navigate('/login');
    return null;
  }

  // Calculate route coordinates for amenity discovery
  const routeCoordinates = optimizedRoute?.coordinates || routePaths[0]?.coordinates || [];

  // Sidebar content with tabs
  const sidebarContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Stops" />
          <Tab label="Amenities" />
          <Tab label="Settings" />
        </Tabs>
      </Box>

      {activeTab === 0 && (
        <StopManagementPanel
          stops={stops}
          onStopsChange={handleStopsChange}
          onOptimizeRoute={handleOptimizeRoute}
          onAddStop={handleStopAdd}
          onCalculateRoute={handleCalculateEnhancedRoute}
          isCalculating={isCalculatingRoute}
        />
      )}

      {activeTab === 1 && (
        <AmenityDiscovery
          routeCoordinates={routeCoordinates}
          onAddAmenityToRoute={handleAmenityAdd}
          selectedAmenityTypes={selectedAmenityTypes}
          onAmenityTypesChange={handleAmenityTypesChange}
        />
      )}

      {activeTab === 2 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Route Settings
          </Typography>

          <Stack spacing={3}>
            <FormControl fullWidth>
              <InputLabel>Vehicle Type</InputLabel>
              <Select
                value={vehicleType}
                onChange={(e) => setVehicleType(e.target.value)}
                label="Vehicle Type"
              >
                <MenuItem value="car">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DirectionsCarIcon />
                    Car
                  </Box>
                </MenuItem>
                <MenuItem value="bike">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DirectionsBikeIcon />
                    Bike
                  </Box>
                </MenuItem>
                <MenuItem value="walk">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DirectionsWalkIcon />
                    Walk
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            <LocationSearch
              onSearchResults={handleSearchResults}
              onCategoryFilter={handleCategoryFilter}
              landmarks={landmarks}
              placeholder="Search for locations..."
              showCategories={true}
            />

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            {optimizedRoute && (
              <Card sx={{ mt: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Route Summary
                  </Typography>
                  <Stack spacing={1}>
                    <Typography variant="body2">
                      Distance: {(optimizedRoute.total_distance || 0).toFixed(1)} km
                    </Typography>
                    <Typography variant="body2">
                      Time: {Math.round((optimizedRoute.total_time || 0) / 60)} minutes
                    </Typography>
                    <Typography variant="body2">
                      Stops: {stops.length}
                    </Typography>
                    {optimizedRoute.optimized && (
                      <Chip label="Optimized Route" color="success" size="small" />
                    )}
                  </Stack>
                </CardContent>
              </Card>
            )}
          </Stack>
        </Box>
      )}
    </Box>
  );

  return (
    <ThemeProvider>
      <EnhancedLayout
        title="Enhanced Route Planner"
        sidebarContent={sidebarContent}
        onQuickAction={handleQuickAction}
        showQuickActions={true}
      >

      <Grid container spacing={4}>
        {/* Route Search Form */}
        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ p: 3, borderRadius: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom fontWeight="600" sx={{ mb: 3 }}>
              Route Settings
            </Typography>

            {/* Enhanced Location Search */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Search for Places
              </Typography>
              <LocationSearch
                onSearchResults={handleSearchResults}
                onCategoryFilter={handleCategoryFilter}
                landmarks={landmarks}
                placeholder="Search restaurants, hospitals, schools..."
                showCategories={true}
              />
            </Box>

            <Stack spacing={3}>
              {/* Multi-stop delivery UI */}
              <DragDropContext onDragEnd={handleDragEnd}>
                <DroppableWrapper droppableId="stops-droppable">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      {stops.map((stop, idx) => (
                        <Draggable key={stop.id} draggableId={`stop-${stop.id}`} index={idx}>
                          {(provided, snapshot) => (
                            <Box
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              sx={{ display: 'flex', alignItems: 'center', mb: 1, background: snapshot.isDragging ? '#e3f2fd' : 'none', borderRadius: 1 }}
                              item={String(true)} // Fix non-boolean attribute warning
                            >
                              <Autocomplete
                                options={landmarks.map(l => l.name)}
                                inputValue={stop.query}
                                value={landmarks.map(l => l.name).includes(stop.query) ? stop.query : null}
                                onInputChange={(_, value) => handleStopQueryChange(idx, value)}
                                onChange={(_, value) => handleStopSelect(idx, value)}
                                renderInput={(params) => <TextField {...params} label={`Stop ${idx + 1}`} />}
                                sx={{ flex: 1 }}
                              />
                              {stops.length > 2 && (
                                <Button color="error" onClick={() => handleRemoveStop(idx)} sx={{ ml: 1, minWidth: 0, px: 1 }}>✕</Button>
                              )}
                            </Box>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </DroppableWrapper>
              </DragDropContext>
              <Button variant="outlined" onClick={handleAddStop} sx={{ mb: 2 }}>+ Add Stop</Button>
              <Button variant="contained" color="secondary" onClick={handleOptimizeRoute} sx={{ width: '100%' }} disabled={stops.filter(s => s.lat && s.lng).length < 2}>
                Optimize Delivery Route
              </Button>
              {/* 3. Add Reset Route button near Optimize button */}
              <Button variant="outlined" color="error" onClick={handleResetRoute} sx={{ mt: 1 }}>
                Reset Route
              </Button>
              {/* Add Test Route button */}
              <Button variant="contained" color="info" onClick={handleTestRoute} sx={{ width: '100%' }} disabled={stops.filter(s => s.lat && s.lng).length < 2 || prototypeRouteLoading}>
                {prototypeRouteLoading ? 'Testing...' : 'Test Route'}
              </Button>
              {prototypeRouteError && (
                <Alert severity="error" sx={{ mt: 2 }}>{prototypeRouteError}</Alert>
              )}
              {prototypeRoute && prototypeRoute.total_distance_km && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Prototype route found! Total distance: {prototypeRoute.total_distance_km.toFixed(2)} km.
                </Alert>
              )}
            </Stack>
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Map and Route Options */}
        <Grid item xs={12} md={8}>
          {/* Enhanced Map Container */}
          <Paper
            elevation={2}
            sx={{
              p: 0,
              borderRadius: 3,
              overflow: 'hidden',
              mb: 3,
              height: '500px'
            }}
          >
            <InteractiveMap
              center={mapCenter}
              zoom={mapZoom}
              customPins={customPins}
              searchResults={searchResults}
              landmarks={landmarks}
              onPinAdd={handlePinAdd}
              onPinUpdate={handlePinUpdate}
              onPinDelete={handlePinDelete}
              onLocationSelect={handleLocationSelect}
              height="500px"
              routePaths={[
                // Existing route paths
                ...routePaths.map(rp => ({
                  coordinates: rp.path,
                  color: rp.color,
                  weight: 4,
                  opacity: 0.7
                })),
                // Prototype route
                ...(prototypeRoute && prototypeRoute.path_coords ? [{
                  coordinates: prototypeRoute.path_coords,
                  color: "#1976d2",
                  weight: 7,
                  opacity: 0.9
                }] : []),
                // Direct route
                ...(directRoute && directRoute.path_coords ? [{
                  coordinates: directRoute.path_coords,
                  color: "#ff5722",
                  weight: 5,
                  opacity: 0.8
                }] : [])
              ]}
            />


          </Paper>

          {/* Route Details and Options */}
          {route && (
            <Paper
              elevation={2}
              sx={{
                p: 3,
                borderRadius: 3
              }}
            >
              <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', mb: 3, gap: 2 }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  py: 1,
                  px: 2,
                  borderRadius: 2,
                  bgcolor: 'primary.light',
                  color: 'white'
                }}>
                  {weatherIcons[route.weather.condition] || weatherIcons.sunny}
                  <Typography variant="body1" sx={{ ml: 1 }}>
                    {route.weather.condition}
                    </Typography>
                  </Box>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  py: 1,
                  px: 2,
                  borderRadius: 2,
                  bgcolor:
                    route.traffic === 'light'
                      ? 'success.light'
                      : route.traffic === 'moderate'
                        ? 'warning.light'
                        : 'error.light',
                  color: 'white'
                }}>
                  {trafficIcons[route.traffic] || trafficIcons.light}
                  <Typography variant="body1" sx={{ ml: 1, textTransform: 'capitalize' }}>
                    {route.traffic} Traffic
                    </Typography>
                  </Box>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  py: 1,
                  px: 2,
                  borderRadius: 2,
                  bgcolor: 'background.default'
                }}>
                  {vehicleIcons[route.vehicle_type] || vehicleIcons.car}
                  <Typography variant="body1" sx={{ ml: 1, textTransform: 'capitalize' }}>
                    {route.vehicle_type}
                  </Typography>
                </Box>
              </Box>

              <Typography variant="h6" gutterBottom fontWeight="600">
                  Route Options
                </Typography>

              <FormControl component="fieldset" sx={{ width: '100%', mb: 3 }}>
                  <RadioGroup
                    value={selectedRouteOption}
                    onChange={handleRouteOptionChange}
                  >
                  <Grid container spacing={2}>
                    {route.route_options.map((option, index) => (
                      <Grid item xs={12} key={index}>
                      <Paper
                          elevation={selectedRouteOption === option.option_name ? 2 : 0}
                        sx={{
                          p: 2,
                            borderRadius: 2,
                            border: `2px solid ${selectedRouteOption === option.option_name
                              ? getPolylineColor(route.vehicle_type, index)
                              : 'transparent'}`,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              bgcolor: 'background.default',
                              transform: 'translateY(-2px)'
                            }
                        }}
                      >
                        <FormControlLabel
                          value={option.option_name}
                          control={<Radio />}
                          label={
                            <Box>
                                <Typography variant="subtitle1" fontWeight="600">
                                  {option.option_name}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  {option.description}
                                </Typography>
                                <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                                  <Chip
                                    size="small"
                                    label={`${option.distance} km`}
                                    sx={{
                                      bgcolor: alpha(getPolylineColor(route.vehicle_type, index), 0.1),
                                      borderRadius: '8px'
                                    }}
                                  />
                                  <Chip
                                    size="small"
                                    label={`${option.duration} min`}
                                    sx={{
                                      bgcolor: alpha(getPolylineColor(route.vehicle_type, index), 0.1),
                                      borderRadius: '8px'
                                    }}
                                  />
                                </Box>
                              </Box>
                            }
                            sx={{
                              width: '100%',
                              alignItems: 'flex-start',
                              ml: 0,
                              '& .MuiFormControlLabel-label': {
                                width: '100%'
                          }
                            }}
                        />
                      </Paper>
                      </Grid>
                    ))}
                  </Grid>
                  </RadioGroup>
                </FormControl>

              {/* Directions */}
                {steps.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom fontWeight="600">
                    Directions
                    </Typography>
                  <Paper
                    elevation={0}
                    sx={{
                      bgcolor: 'background.default',
                      borderRadius: 2,
                      maxHeight: '300px',
                      overflow: 'auto'
                    }}
                  >
                    <List dense>
                      {steps.map((step, index) => (
                        <React.Fragment key={index}>
                          <ListItem>
                          <ListItemIcon>
                            {getDirectionIcon(step.instruction)}
                          </ListItemIcon>
                          <ListItemText
                            primary={step.instruction.charAt(0).toUpperCase() + step.instruction.slice(1)}
                              secondary={`${formatDistance(step.distance)} - ${formatDuration(step.duration)}`}
                          />
                        </ListItem>
                          {index < steps.length - 1 && <Divider variant="inset" component="li" />}
                        </React.Fragment>
                      ))}
                    </List>
                  </Paper>
                </>
            )}
            {liveDistance !== null && userCoords && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  You are {formatDistance(liveDistance)} from your destination. Map updates as you move.
                </Alert>
              )}
          </Paper>
          )}
        </Grid>
      </Grid>

      {/* Share Route Dialog */}
      <Dialog open={shareDialogOpen} onClose={() => setShareDialogOpen(false)}>
        <DialogTitle>Share Route (Google Maps)</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>{shareUrl}</Typography>
          {/* Embed Google Maps iframe if possible */}
          {(() => {
            const validStops = stops.filter(s => s.lat && s.lng);
            if (
              shareType === 'google' &&
              GOOGLE_MAPS_EMBED_API_KEY &&
              validStops.length >= 2 &&
              validStops.length <= 10
            ) {
              const embedUrl = getGoogleMapsEmbedUrl(stops);
              return (
                <Box sx={{ mt: 2 }}>
                  <iframe
                    title="Google Maps Directions"
                    width="100%"
                    height="400"
                    frameBorder="0"
                    style={{ border: 0 }}
                    src={embedUrl}
                    allowFullScreen
                  />
                  <Typography variant="caption" color="text.secondary">
                    (Up to 10 stops supported. For more, use the Open button below.)
                  </Typography>
                </Box>
              );
            }
            return null;
          })()}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCopyShareUrl} startIcon={<ContentCopyIcon />}>Copy Link</Button>
          <Button onClick={() => window.open(shareUrl, '_blank')} startIcon={<ShareIcon />}>Open</Button>
          <Button onClick={() => setShareDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Legend for route types */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 24, height: 4, bgcolor: '#8e24aa', mr: 1 }} />
          <Typography variant="body2">Direct Route (Greedy)</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 24, height: 4, bgcolor: '#1976d2', mr: 1 }} />
          <Typography variant="body2">Floyd-Warshall Route</Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default FindRoute;