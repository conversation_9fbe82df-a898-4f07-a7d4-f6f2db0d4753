#!/usr/bin/env python3
"""
Test script for the route calculation API
"""
import requests
import json

def test_route_calculation():
    """Test the route calculation endpoint"""
    
    # Test data
    test_data = {
        "stops": [
            {
                "id": "test1",
                "name": "Test Stop 1",
                "lat": 30.3165,
                "lng": 78.0322,
                "order": 0,
                "stop_type": "start"
            },
            {
                "id": "test2", 
                "name": "Test Stop 2",
                "lat": 30.3200,
                "lng": 78.0350,
                "order": 1,
                "stop_type": "end"
            }
        ],
        "optimize_order": False,
        "vehicle_type": "car"
    }
    
    try:
        print("Testing route calculation API...")
        print(f"Request data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            "http://localhost:8000/routes/calculate-with-stops",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Response data: {json.dumps(result, indent=2)}")
        else:
            print("❌ FAILED!")
            print(f"Error response: {response.text}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    test_route_calculation()
