import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  IconButton,
  Button,
  Card,
  CardContent,
  Chip,
  Stack,
  Divider,
  InputAdornment,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  MyLocation as LocationIcon,
  Flag as FlagIcon,
  Place as PlaceIcon,
  Settings as OptimizeIcon,
  LocationOn as LocationOnIcon,
  Star as StarIcon,
  Navigation as NavigationIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

// Famous places in Dehradun with coordinates
const FAMOUS_PLACES = [
  { name: 'Clock Tower (Ghanta Ghar)', lat: 30.3165, lng: 78.0322, category: 'Landmark' },
  { name: '<PERSON><PERSON>\'s Cave (<PERSON><PERSON><PERSON>)', lat: 30.3732, lng: 78.0167, category: 'Tourist Spot' },
  { name: '<PERSON><PERSON><PERSON><PERSON>ra', lat: 30.3732, lng: 78.0167, category: 'Tourist Spot' },
  { name: 'Forest Research Institute (FRI)', lat: 30.3398, lng: 77.9993, category: 'Educational' },
  { name: 'Tapkeshwar Temple', lat: 30.3732, lng: 78.0167, category: 'Religious' },
  { name: 'Mindrolling Monastery', lat: 30.3398, lng: 78.0322, category: 'Religious' },
  { name: 'Rajaji National Park', lat: 30.0869, lng: 78.2676, category: 'Nature' },
  { name: 'Lacchiwala', lat: 30.2165, lng: 78.0322, category: 'Nature' },
  { name: 'Malsi Deer Park', lat: 30.3732, lng: 78.0167, category: 'Nature' },
  { name: 'Dehradun Railway Station', lat: 30.3165, lng: 78.0322, category: 'Transport' },
  { name: 'Jolly Grant Airport', lat: 30.1897, lng: 78.1804, category: 'Transport' },
  { name: 'ISBT Dehradun', lat: 30.3165, lng: 78.0322, category: 'Transport' },
  { name: 'Paltan Bazaar', lat: 30.3165, lng: 78.0322, category: 'Shopping' },
  { name: 'Rajpur Road', lat: 30.3398, lng: 78.0322, category: 'Shopping' },
  { name: 'Pacific Mall', lat: 30.3398, lng: 78.0322, category: 'Shopping' },
  { name: 'Doon University', lat: 30.3398, lng: 78.0322, category: 'Educational' },
  { name: 'IIT Roorkee Dehradun Campus', lat: 30.3398, lng: 78.0322, category: 'Educational' },
  { name: 'Graphic Era University', lat: 30.3398, lng: 78.0322, category: 'Educational' },
  { name: 'Clement Town', lat: 30.2665, lng: 78.0322, category: 'Residential' },
  { name: 'Mussoorie Road', lat: 30.3398, lng: 78.0322, category: 'Area' }
];

const StopManagementPanel = ({
  stops = [],
  onStopsChange,
  onOptimizeRoute,
  onAddStop,
  onCalculateRoute,
  isCalculating = false
}) => {
  const [searchFilter, setSearchFilter] = useState('');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingStop, setEditingStop] = useState(null);
  const [coordinateDialogOpen, setCoordinateDialogOpen] = useState(false);
  const [newCoordinates, setNewCoordinates] = useState({ lat: '', lng: '', name: '' });

  // Enhanced stop addition state
  const [addStopDialogOpen, setAddStopDialogOpen] = useState(false);
  const [addStopMethod, setAddStopMethod] = useState(0); // 0: Famous Places, 1: Search, 2: Coordinates, 3: Current Location
  const [selectedFamousPlace, setSelectedFamousPlace] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Filter stops based on search
  const filteredStops = stops.filter(stop =>
    (stop.name || stop.query || '').toLowerCase().includes(searchFilter.toLowerCase())
  );

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(stops);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order numbers
    const updatedStops = items.map((stop, index) => ({
      ...stop,
      order: index
    }));

    onStopsChange(updatedStops);
  };

  const handleEditStop = (stop) => {
    setEditingStop({ ...stop });
    setEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (editingStop) {
      const updatedStops = stops.map(stop =>
        stop.id === editingStop.id ? editingStop : stop
      );
      onStopsChange(updatedStops);
      setEditDialogOpen(false);
      setEditingStop(null);
    }
  };

  const handleDeleteStop = (stopId) => {
    const updatedStops = stops.filter(stop => stop.id !== stopId)
      .map((stop, index) => ({ ...stop, order: index }));
    onStopsChange(updatedStops);
  };

  const handleSetAsStart = (stopId) => {
    const updatedStops = stops.map(stop => ({
      ...stop,
      stop_type: stop.id === stopId ? 'start' :
                 stop.stop_type === 'start' ? 'waypoint' : stop.stop_type
    }));
    onStopsChange(updatedStops);
  };

  const handleSetAsEnd = (stopId) => {
    const updatedStops = stops.map(stop => ({
      ...stop,
      stop_type: stop.id === stopId ? 'end' :
                 stop.stop_type === 'end' ? 'waypoint' : stop.stop_type
    }));
    onStopsChange(updatedStops);
  };

  const handleAddCoordinates = () => {
    if (newCoordinates.lat && newCoordinates.lng && newCoordinates.name) {
      const lat = parseFloat(newCoordinates.lat);
      const lng = parseFloat(newCoordinates.lng);

      if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        alert('Please enter valid coordinates (Latitude: -90 to 90, Longitude: -180 to 180)');
        return;
      }

      const newStop = {
        id: `coord_${Date.now()}`,
        name: newCoordinates.name,
        lat: lat,
        lng: lng,
        order: stops.length,
        stop_type: 'waypoint'
      };

      onAddStop(newStop);
      setNewCoordinates({ lat: '', lng: '', name: '' });
      setCoordinateDialogOpen(false);
    }
  };

  // Enhanced stop addition handlers
  const handleAddFamousPlace = () => {
    if (selectedFamousPlace) {
      const newStop = {
        id: `famous_${Date.now()}`,
        name: selectedFamousPlace.name,
        lat: selectedFamousPlace.lat,
        lng: selectedFamousPlace.lng,
        order: stops.length,
        stop_type: stops.length === 0 ? 'start' : 'waypoint',
        category: selectedFamousPlace.category
      };

      onAddStop(newStop);
      setSelectedFamousPlace(null);
      setAddStopDialogOpen(false);
    }
  };

  const handleAddCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const newStop = {
          id: `current_${Date.now()}`,
          name: 'Current Location',
          lat: latitude,
          lng: longitude,
          order: stops.length,
          stop_type: stops.length === 0 ? 'start' : 'waypoint'
        };

        onAddStop(newStop);
        setIsGettingLocation(false);
        setAddStopDialogOpen(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        alert('Unable to get your current location. Please try again.');
        setIsGettingLocation(false);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
    );
  };

  const handleAddSearchLocation = () => {
    if (searchQuery.trim()) {
      // This would typically integrate with a geocoding service
      // For now, we'll create a placeholder stop
      const newStop = {
        id: `search_${Date.now()}`,
        name: searchQuery,
        lat: 30.3165, // Default to Dehradun center
        lng: 78.0322,
        order: stops.length,
        stop_type: stops.length === 0 ? 'start' : 'waypoint',
        query: searchQuery
      };

      onAddStop(newStop);
      setSearchQuery('');
      setAddStopDialogOpen(false);
    }
  };

  const getStopIcon = (stopType) => {
    switch (stopType) {
      case 'start': return <FlagIcon color="success" />;
      case 'end': return <FlagIcon color="error" />;
      default: return <PlaceIcon color="primary" />;
    }
  };

  const getStopTypeColor = (stopType) => {
    switch (stopType) {
      case 'start': return 'success';
      case 'end': return 'error';
      default: return 'primary';
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom>
          Stop Management ({stops.length})
        </Typography>

        {/* Search Filter */}
        <TextField
          fullWidth
          size="small"
          placeholder="Search stops..."
          value={searchFilter}
          onChange={(e) => setSearchFilter(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Action Buttons */}
        <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 2 }}>
          <Button
            size="small"
            startIcon={<AddIcon />}
            onClick={() => setAddStopDialogOpen(true)}
            variant="contained"
            color="primary"
          >
            Add Stop
          </Button>
          <Button
            size="small"
            startIcon={<OptimizeIcon />}
            onClick={onOptimizeRoute}
            disabled={stops.length < 3 || isCalculating}
            variant="outlined"
          >
            Optimize
          </Button>
          <Button
            size="small"
            onClick={onCalculateRoute}
            disabled={stops.length < 2 || isCalculating}
            variant="contained"
            color="success"
          >
            Calculate Route
          </Button>
        </Stack>

        {/* Stop Count Info */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {stops.length} stop{stops.length !== 1 ? 's' : ''} added
          {stops.length > 0 && (
            <span> • Right-click on map to add more stops</span>
          )}
        </Typography>
      </Box>

      {/* Stops List */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {filteredStops.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <PlaceIcon sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="body2">
              {stops.length === 0 ? 'No stops added yet' : 'No stops match your search'}
            </Typography>
          </Box>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="stops">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {filteredStops.map((stop, index) => (
                    <Draggable key={stop.id} draggableId={stop.id} index={index}>
                      {(provided, snapshot) => (
                        <Card
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          sx={{
                            mb: 1,
                            opacity: snapshot.isDragging ? 0.8 : 1,
                            transform: snapshot.isDragging ? 'rotate(5deg)' : 'none',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              {/* Drag Handle */}
                              <Box {...provided.dragHandleProps}>
                                <DragIcon sx={{ color: 'text.secondary', cursor: 'grab' }} />
                              </Box>

                              {/* Stop Number */}
                              <Chip
                                label={index + 1}
                                size="small"
                                color={getStopTypeColor(stop.stop_type)}
                                sx={{ minWidth: 32 }}
                              />

                              {/* Stop Info */}
                              <Box sx={{ flex: 1, minWidth: 0 }}>
                                <Stack direction="row" alignItems="center" spacing={1}>
                                  {getStopIcon(stop.stop_type)}
                                  <Typography variant="body2" noWrap>
                                    {stop.name || stop.query || `Stop ${index + 1}`}
                                  </Typography>
                                </Stack>
                                <Typography variant="caption" color="text.secondary">
                                  {stop.lat && stop.lng ? `${stop.lat.toFixed(4)}, ${stop.lng.toFixed(4)}` : 'No coordinates'}
                                </Typography>
                              </Box>

                              {/* Actions */}
                              <Stack direction="row" spacing={0.5}>
                                <Tooltip title="Edit">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleEditStop(stop)}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Delete">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleDeleteStop(stop.id)}
                                    color="error"
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Stack>
                            </Stack>

                            {/* Stop Type Actions */}
                            {stop.stop_type !== 'start' && stop.stop_type !== 'end' && (
                              <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                                <Button
                                  size="small"
                                  onClick={() => handleSetAsStart(stop.id)}
                                  variant="text"
                                  color="success"
                                >
                                  Set as Start
                                </Button>
                                <Button
                                  size="small"
                                  onClick={() => handleSetAsEnd(stop.id)}
                                  variant="text"
                                  color="error"
                                >
                                  Set as End
                                </Button>
                              </Stack>
                            )}
                          </CardContent>
                        </Card>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </Box>

      {/* Edit Stop Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Stop</DialogTitle>
        <DialogContent>
          {editingStop && (
            <Stack spacing={2} sx={{ mt: 1 }}>
              <TextField
                label="Stop Name"
                value={editingStop.name || ''}
                onChange={(e) => setEditingStop({ ...editingStop, name: e.target.value })}
                fullWidth
              />
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    label="Latitude"
                    type="number"
                    value={editingStop.lat || ''}
                    onChange={(e) => setEditingStop({ ...editingStop, lat: parseFloat(e.target.value) || null })}
                    fullWidth
                    inputProps={{ step: 0.000001 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    label="Longitude"
                    type="number"
                    value={editingStop.lng || ''}
                    onChange={(e) => setEditingStop({ ...editingStop, lng: parseFloat(e.target.value) || null })}
                    fullWidth
                    inputProps={{ step: 0.000001 }}
                  />
                </Grid>
              </Grid>
              <FormControl fullWidth>
                <InputLabel>Stop Type</InputLabel>
                <Select
                  value={editingStop.stop_type}
                  onChange={(e) => setEditingStop({ ...editingStop, stop_type: e.target.value })}
                  label="Stop Type"
                >
                  <MenuItem value="start">Start Point</MenuItem>
                  <MenuItem value="waypoint">Waypoint</MenuItem>
                  <MenuItem value="end">End Point</MenuItem>
                </Select>
              </FormControl>
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveEdit} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Add Coordinates Dialog */}
      <Dialog open={coordinateDialogOpen} onClose={() => setCoordinateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Stop by Coordinates</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            <TextField
              label="Location Name"
              value={newCoordinates.name}
              onChange={(e) => setNewCoordinates({ ...newCoordinates, name: e.target.value })}
              fullWidth
              placeholder="e.g., My Office"
            />
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  label="Latitude"
                  type="number"
                  value={newCoordinates.lat}
                  onChange={(e) => setNewCoordinates({ ...newCoordinates, lat: e.target.value })}
                  fullWidth
                  placeholder="30.3165"
                  inputProps={{ step: 0.000001 }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  label="Longitude"
                  type="number"
                  value={newCoordinates.lng}
                  onChange={(e) => setNewCoordinates({ ...newCoordinates, lng: e.target.value })}
                  fullWidth
                  placeholder="78.0322"
                  inputProps={{ step: 0.000001 }}
                />
              </Grid>
            </Grid>
            <Typography variant="caption" color="text.secondary">
              Enter coordinates in decimal degrees format. For Dehradun area: Lat ~30.31, Lng ~78.03
            </Typography>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCoordinateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddCoordinates} variant="contained">Add Stop</Button>
        </DialogActions>
      </Dialog>

      {/* Enhanced Add Stop Dialog */}
      <Dialog
        open={addStopDialogOpen}
        onClose={() => setAddStopDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Stop</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Tabs
              value={addStopMethod}
              onChange={(e, newValue) => setAddStopMethod(newValue)}
              sx={{ mb: 3 }}
            >
              <Tab label="Famous Places" icon={<StarIcon />} />
              <Tab label="Search" icon={<SearchIcon />} />
              <Tab label="Coordinates" icon={<LocationOnIcon />} />
              <Tab label="Current Location" icon={<NavigationIcon />} />
            </Tabs>

            {/* Famous Places Tab */}
            {addStopMethod === 0 && (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Select from popular Dehradun locations:
                </Typography>
                <Autocomplete
                  options={FAMOUS_PLACES}
                  getOptionLabel={(option) => option.name}
                  groupBy={(option) => option.category}
                  value={selectedFamousPlace}
                  onChange={(event, newValue) => setSelectedFamousPlace(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Choose a famous place"
                      placeholder="Search famous places..."
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <Box>
                        <Typography variant="body2">{option.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.category}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                />
              </Box>
            )}

            {/* Search Tab */}
            {addStopMethod === 1 && (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Search for any location in Dehradun:
                </Typography>
                <TextField
                  fullWidth
                  label="Search Location"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="e.g., Rajpur Road, Clock Tower..."
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            )}

            {/* Coordinates Tab */}
            {addStopMethod === 2 && (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Enter precise coordinates:
                </Typography>
                <Stack spacing={2}>
                  <TextField
                    label="Location Name"
                    value={newCoordinates.name}
                    onChange={(e) => setNewCoordinates({ ...newCoordinates, name: e.target.value })}
                    fullWidth
                    placeholder="e.g., My Office"
                  />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        label="Latitude"
                        type="number"
                        value={newCoordinates.lat}
                        onChange={(e) => setNewCoordinates({ ...newCoordinates, lat: e.target.value })}
                        fullWidth
                        placeholder="30.3165"
                        inputProps={{ step: 0.000001 }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        label="Longitude"
                        type="number"
                        value={newCoordinates.lng}
                        onChange={(e) => setNewCoordinates({ ...newCoordinates, lng: e.target.value })}
                        fullWidth
                        placeholder="78.0322"
                        inputProps={{ step: 0.000001 }}
                      />
                    </Grid>
                  </Grid>
                </Stack>
              </Box>
            )}

            {/* Current Location Tab */}
            {addStopMethod === 3 && (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <NavigationIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Use Your Current Location
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  This will add your current GPS location as a stop.
                </Typography>
                {isGettingLocation && (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                    <CircularProgress size={20} />
                    <Typography variant="body2">Getting your location...</Typography>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddStopDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => {
              switch (addStopMethod) {
                case 0: handleAddFamousPlace(); break;
                case 1: handleAddSearchLocation(); break;
                case 2: handleAddCoordinates(); break;
                case 3: handleAddCurrentLocation(); break;
                default: break;
              }
            }}
            variant="contained"
            disabled={
              (addStopMethod === 0 && !selectedFamousPlace) ||
              (addStopMethod === 1 && !searchQuery.trim()) ||
              (addStopMethod === 2 && (!newCoordinates.lat || !newCoordinates.lng || !newCoordinates.name)) ||
              isGettingLocation
            }
          >
            {isGettingLocation ? 'Getting Location...' : 'Add Stop'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StopManagementPanel;
