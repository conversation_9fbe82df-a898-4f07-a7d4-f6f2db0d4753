import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  IconButton,
  Button,
  Card,
  CardContent,
  Chip,
  Stack,
  Divider,
  InputAdornment,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  MyLocation as LocationIcon,
  Flag as FlagIcon,
  Place as PlaceIcon,
  Optimize as OptimizeIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const StopManagementPanel = ({
  stops = [],
  onStopsChange,
  onOptimizeRoute,
  onAddStop,
  onCalculateRoute,
  isCalculating = false
}) => {
  const [searchFilter, setSearchFilter] = useState('');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingStop, setEditingStop] = useState(null);
  const [coordinateDialogOpen, setCoordinateDialogOpen] = useState(false);
  const [newCoordinates, setNewCoordinates] = useState({ lat: '', lng: '', name: '' });

  // Filter stops based on search
  const filteredStops = stops.filter(stop =>
    stop.name.toLowerCase().includes(searchFilter.toLowerCase())
  );

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const items = Array.from(stops);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order numbers
    const updatedStops = items.map((stop, index) => ({
      ...stop,
      order: index
    }));

    onStopsChange(updatedStops);
  };

  const handleEditStop = (stop) => {
    setEditingStop({ ...stop });
    setEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (editingStop) {
      const updatedStops = stops.map(stop =>
        stop.id === editingStop.id ? editingStop : stop
      );
      onStopsChange(updatedStops);
      setEditDialogOpen(false);
      setEditingStop(null);
    }
  };

  const handleDeleteStop = (stopId) => {
    const updatedStops = stops.filter(stop => stop.id !== stopId)
      .map((stop, index) => ({ ...stop, order: index }));
    onStopsChange(updatedStops);
  };

  const handleSetAsStart = (stopId) => {
    const updatedStops = stops.map(stop => ({
      ...stop,
      stop_type: stop.id === stopId ? 'start' : 
                 stop.stop_type === 'start' ? 'waypoint' : stop.stop_type
    }));
    onStopsChange(updatedStops);
  };

  const handleSetAsEnd = (stopId) => {
    const updatedStops = stops.map(stop => ({
      ...stop,
      stop_type: stop.id === stopId ? 'end' : 
                 stop.stop_type === 'end' ? 'waypoint' : stop.stop_type
    }));
    onStopsChange(updatedStops);
  };

  const handleAddCoordinates = () => {
    if (newCoordinates.lat && newCoordinates.lng && newCoordinates.name) {
      const lat = parseFloat(newCoordinates.lat);
      const lng = parseFloat(newCoordinates.lng);
      
      if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        alert('Please enter valid coordinates (Latitude: -90 to 90, Longitude: -180 to 180)');
        return;
      }

      const newStop = {
        id: `coord_${Date.now()}`,
        name: newCoordinates.name,
        lat: lat,
        lng: lng,
        order: stops.length,
        stop_type: 'waypoint'
      };

      onAddStop(newStop);
      setNewCoordinates({ lat: '', lng: '', name: '' });
      setCoordinateDialogOpen(false);
    }
  };

  const getStopIcon = (stopType) => {
    switch (stopType) {
      case 'start': return <FlagIcon color="success" />;
      case 'end': return <FlagIcon color="error" />;
      default: return <PlaceIcon color="primary" />;
    }
  };

  const getStopTypeColor = (stopType) => {
    switch (stopType) {
      case 'start': return 'success';
      case 'end': return 'error';
      default: return 'primary';
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom>
          Stop Management ({stops.length})
        </Typography>
        
        {/* Search Filter */}
        <TextField
          fullWidth
          size="small"
          placeholder="Search stops..."
          value={searchFilter}
          onChange={(e) => setSearchFilter(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Action Buttons */}
        <Stack direction="row" spacing={1} flexWrap="wrap">
          <Button
            size="small"
            startIcon={<AddIcon />}
            onClick={() => setCoordinateDialogOpen(true)}
            variant="outlined"
          >
            Add Coordinates
          </Button>
          <Button
            size="small"
            startIcon={<OptimizeIcon />}
            onClick={onOptimizeRoute}
            disabled={stops.length < 3 || isCalculating}
            variant="outlined"
          >
            Optimize
          </Button>
          <Button
            size="small"
            onClick={onCalculateRoute}
            disabled={stops.length < 2 || isCalculating}
            variant="contained"
          >
            Calculate Route
          </Button>
        </Stack>
      </Box>

      {/* Stops List */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {filteredStops.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <PlaceIcon sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="body2">
              {stops.length === 0 ? 'No stops added yet' : 'No stops match your search'}
            </Typography>
          </Box>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="stops">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {filteredStops.map((stop, index) => (
                    <Draggable key={stop.id} draggableId={stop.id} index={index}>
                      {(provided, snapshot) => (
                        <Card
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          sx={{
                            mb: 1,
                            opacity: snapshot.isDragging ? 0.8 : 1,
                            transform: snapshot.isDragging ? 'rotate(5deg)' : 'none',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              {/* Drag Handle */}
                              <Box {...provided.dragHandleProps}>
                                <DragIcon sx={{ color: 'text.secondary', cursor: 'grab' }} />
                              </Box>

                              {/* Stop Number */}
                              <Chip
                                label={index + 1}
                                size="small"
                                color={getStopTypeColor(stop.stop_type)}
                                sx={{ minWidth: 32 }}
                              />

                              {/* Stop Info */}
                              <Box sx={{ flex: 1, minWidth: 0 }}>
                                <Stack direction="row" alignItems="center" spacing={1}>
                                  {getStopIcon(stop.stop_type)}
                                  <Typography variant="body2" noWrap>
                                    {stop.name}
                                  </Typography>
                                </Stack>
                                <Typography variant="caption" color="text.secondary">
                                  {stop.lat.toFixed(4)}, {stop.lng.toFixed(4)}
                                </Typography>
                              </Box>

                              {/* Actions */}
                              <Stack direction="row" spacing={0.5}>
                                <Tooltip title="Edit">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleEditStop(stop)}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Delete">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleDeleteStop(stop.id)}
                                    color="error"
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Stack>
                            </Stack>

                            {/* Stop Type Actions */}
                            {stop.stop_type !== 'start' && stop.stop_type !== 'end' && (
                              <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                                <Button
                                  size="small"
                                  onClick={() => handleSetAsStart(stop.id)}
                                  variant="text"
                                  color="success"
                                >
                                  Set as Start
                                </Button>
                                <Button
                                  size="small"
                                  onClick={() => handleSetAsEnd(stop.id)}
                                  variant="text"
                                  color="error"
                                >
                                  Set as End
                                </Button>
                              </Stack>
                            )}
                          </CardContent>
                        </Card>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </Box>

      {/* Edit Stop Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Stop</DialogTitle>
        <DialogContent>
          {editingStop && (
            <Stack spacing={2} sx={{ mt: 1 }}>
              <TextField
                label="Stop Name"
                value={editingStop.name}
                onChange={(e) => setEditingStop({ ...editingStop, name: e.target.value })}
                fullWidth
              />
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    label="Latitude"
                    type="number"
                    value={editingStop.lat}
                    onChange={(e) => setEditingStop({ ...editingStop, lat: parseFloat(e.target.value) })}
                    fullWidth
                    inputProps={{ step: 0.000001 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    label="Longitude"
                    type="number"
                    value={editingStop.lng}
                    onChange={(e) => setEditingStop({ ...editingStop, lng: parseFloat(e.target.value) })}
                    fullWidth
                    inputProps={{ step: 0.000001 }}
                  />
                </Grid>
              </Grid>
              <FormControl fullWidth>
                <InputLabel>Stop Type</InputLabel>
                <Select
                  value={editingStop.stop_type}
                  onChange={(e) => setEditingStop({ ...editingStop, stop_type: e.target.value })}
                  label="Stop Type"
                >
                  <MenuItem value="start">Start Point</MenuItem>
                  <MenuItem value="waypoint">Waypoint</MenuItem>
                  <MenuItem value="end">End Point</MenuItem>
                </Select>
              </FormControl>
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveEdit} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Add Coordinates Dialog */}
      <Dialog open={coordinateDialogOpen} onClose={() => setCoordinateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Stop by Coordinates</DialogTitle>
        <DialogContent>
          <Stack spacing={2} sx={{ mt: 1 }}>
            <TextField
              label="Location Name"
              value={newCoordinates.name}
              onChange={(e) => setNewCoordinates({ ...newCoordinates, name: e.target.value })}
              fullWidth
              placeholder="e.g., My Office"
            />
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  label="Latitude"
                  type="number"
                  value={newCoordinates.lat}
                  onChange={(e) => setNewCoordinates({ ...newCoordinates, lat: e.target.value })}
                  fullWidth
                  placeholder="30.3165"
                  inputProps={{ step: 0.000001 }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  label="Longitude"
                  type="number"
                  value={newCoordinates.lng}
                  onChange={(e) => setNewCoordinates({ ...newCoordinates, lng: e.target.value })}
                  fullWidth
                  placeholder="78.0322"
                  inputProps={{ step: 0.000001 }}
                />
              </Grid>
            </Grid>
            <Typography variant="caption" color="text.secondary">
              Enter coordinates in decimal degrees format. For Dehradun area: Lat ~30.31, Lng ~78.03
            </Typography>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCoordinateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddCoordinates} variant="contained">Add Stop</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StopManagementPanel;
