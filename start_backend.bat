@echo off
echo Starting Optimal Delivery Route Finder Backend...
echo.
echo Backend will be available at: http://localhost:8000
echo API documentation will be available at: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo.

cd backend
C:\Users\<USER>\OneDrive\Desktop\coding\Optimal-Delivery-Route-Finder\backend\venv\Scripts\python.exe C:\Users\<USER>\OneDrive\Desktop\coding\Optimal-Delivery-Route-Finder\backend\run_server.py

pause
