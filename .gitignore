# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnp
.pnp.js

# Python virtual environment
venv/
env/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS specific files
.DS_Store
Thumbs.db

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Build files
/frontend/build/
/frontend/dist/

# Database files
*.db
*.sqlite
*.sqlite3

# Log files
logs/
*.log

# Test files and coverage
coverage/
.nyc_output
.jest/
test-results/

# Temporary files
*.tmp
*.temp
.cache/

# Package lock files (optional - uncomment if you want to ignore them)
# package-lock.json
# yarn.lock