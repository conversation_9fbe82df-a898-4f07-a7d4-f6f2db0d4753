import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Alert,
  Stack,
  Tabs,
  Tab
} from '@mui/material';
import {
  DirectionsCar as DirectionsCarIcon,
  DirectionsBike as DirectionsBikeIcon,
  DirectionsWalk as DirectionsWalkIcon
} from '@mui/icons-material';
import axios from 'axios';
import L from 'leaflet';
import { useAuth } from '../contexts/AuthContext';

// Enhanced components
import EnhancedLayout from '../components/EnhancedLayout';
import StopManagementPanel from '../components/StopManagementPanel';
import AmenityDiscovery from '../components/AmenityDiscovery';
import InteractiveMap from '../components/InteractiveMap';
import LocationSearch from '../components/LocationSearch';
import { ThemeProvider } from '../theme/ThemeProvider';

const EnhancedFindRoute = () => {
  const { user } = useAuth();
  const mapRef = useRef();

  // Core state
  const [vehicleType, setVehicleType] = useState('car');
  const [error, setError] = useState(null);
  const [landmarks, setLandmarks] = useState([]);
  const [mapCenter, setMapCenter] = useState([30.3165, 78.0322]); // Dehradun center
  const [mapZoom, setMapZoom] = useState(12); // Optimal zoom for Dehradun city view

  // Enhanced features state
  const [stops, setStops] = useState([]);
  const [amenities, setAmenities] = useState([]);
  const [selectedAmenityTypes, setSelectedAmenityTypes] = useState([]);
  const [selectedStopId, setSelectedStopId] = useState(null);
  const [isCalculatingRoute, setIsCalculatingRoute] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [customPins, setCustomPins] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [optimizedRoute, setOptimizedRoute] = useState(null);
  const [routePaths, setRoutePaths] = useState([]);

  // Load landmarks on component mount
  useEffect(() => {
    const loadLandmarks = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('http://localhost:8000/landmarks', {
          headers: { Authorization: `Bearer ${token}` }
        });
        setLandmarks(response.data);
      } catch (error) {
        console.error('Error loading landmarks:', error);
        setError('Failed to load landmarks');
      }
    };

    loadLandmarks();
  }, []);

  // Enhanced stop management handlers
  const handleStopAdd = useCallback((stop) => {
    console.log('Adding stop:', stop);
    const newStop = {
      ...stop,
      id: stop.id || `stop_${Date.now()}_${Math.random()}`,
      order: stops.length
    };
    console.log('New stop created:', newStop);
    setStops(prev => {
      const updated = [...prev, newStop];
      console.log('Updated stops:', updated);
      return updated;
    });
  }, [stops.length]);

  const handleStopEdit = useCallback((stop) => {
    setStops(prev => prev.map(s => s.id === stop.id ? stop : s));
  }, []);

  const handleStopDelete = useCallback((stopId) => {
    setStops(prev => prev.filter(s => s.id !== stopId)
      .map((s, index) => ({ ...s, order: index })));
  }, []);

  const handleStopSelect = useCallback((stop) => {
    setSelectedStopId(stop.id);
  }, []);

  const handleCalculateEnhancedRoute = useCallback(async () => {
    if (stops.length < 2) {
      setError('Please add at least 2 stops to calculate a route.');
      return;
    }

    setIsCalculatingRoute(true);
    setError('');

    try {
      const token = localStorage.getItem('token');

      // Prepare stops data with proper structure
      const stopsData = stops.map((stop, index) => ({
        id: stop.id,
        name: stop.name,
        lat: parseFloat(stop.lat),
        lng: parseFloat(stop.lng),
        order: index,
        stop_type: index === 0 ? 'start' :
                   index === stops.length - 1 && stops.length > 1 ? 'end' :
                   'waypoint'
      }));

      console.log('Calculating route with stops:', stopsData);

      const response = await axios.post(
        'http://localhost:8000/routes/calculate-with-stops',
        {
          stops: stopsData,
          optimize_order: false,
          vehicle_type: vehicleType
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      console.log('Route calculation response:', response.data);
      console.log('Response coordinates:', response.data?.coordinates);
      console.log('Response total_distance:', response.data?.total_distance);
      console.log('Response total_time:', response.data?.total_time);

      if (response.data && response.data.coordinates) {
        setOptimizedRoute(response.data);
        setRoutePaths([{
          coordinates: response.data.coordinates,
          color: '#2563eb',
          weight: 4,
          opacity: 0.8
        }]);

        // Auto-fit map to show all stops and route
        if (mapRef.current) {
          const bounds = L.latLngBounds(stops.map(stop => [stop.lat, stop.lng]));
          mapRef.current.fitBounds(bounds, { padding: [20, 20] });
        }
      } else {
        throw new Error('Invalid route response');
      }
    } catch (error) {
      console.error('Route calculation failed:', error);
      setError(
        error.response?.data?.detail ||
        error.response?.data?.error ||
        'Failed to calculate route. Please try again.'
      );
    } finally {
      setIsCalculatingRoute(false);
    }
  }, [stops, vehicleType]);

  const handleOptimizeRoute = useCallback(async () => {
    if (stops.length < 3) {
      setError('Please add at least 3 stops to optimize the route.');
      return;
    }

    setIsCalculatingRoute(true);
    setError('');

    try {
      const token = localStorage.getItem('token');

      // Prepare stops data for optimization
      const stopsData = stops.map((stop, index) => ({
        id: stop.id,
        name: stop.name,
        lat: parseFloat(stop.lat),
        lng: parseFloat(stop.lng),
        order: index,
        stop_type: index === 0 ? 'start' :
                   index === stops.length - 1 && stops.length > 1 ? 'end' :
                   'waypoint'
      }));

      console.log('Optimizing route with stops:', stopsData);

      const response = await axios.post(
        'http://localhost:8000/routes/calculate-with-stops',
        {
          stops: stopsData,
          optimize_order: true, // Enable optimization
          vehicle_type: vehicleType
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      console.log('Route optimization response:', response.data);

      if (response.data && response.data.coordinates) {
        setOptimizedRoute(response.data);
        setRoutePaths([{
          coordinates: response.data.coordinates,
          color: '#16a34a', // Green for optimized route
          weight: 4,
          opacity: 0.8
        }]);

        // Update stops order if optimization changed it
        if (response.data.ordered_stops) {
          const reorderedStops = response.data.ordered_stops.map((orderedStop, index) => {
            const originalStop = stops.find(s =>
              Math.abs(s.lat - orderedStop.lat) < 0.0001 &&
              Math.abs(s.lng - orderedStop.lng) < 0.0001
            );
            return {
              ...originalStop,
              order: index,
              stop_type: index === 0 ? 'start' :
                        index === response.data.ordered_stops.length - 1 ? 'end' :
                        'waypoint'
            };
          });
          setStops(reorderedStops);
        }

        // Auto-fit map to show all stops and route
        if (mapRef.current) {
          const bounds = L.latLngBounds(stops.map(stop => [stop.lat, stop.lng]));
          mapRef.current.fitBounds(bounds, { padding: [20, 20] });
        }
      } else {
        throw new Error('Invalid optimization response');
      }
    } catch (error) {
      console.error('Route optimization failed:', error);
      setError(
        error.response?.data?.detail ||
        error.response?.data?.error ||
        'Failed to optimize route. Please try again.'
      );
    } finally {
      setIsCalculatingRoute(false);
    }
  }, [stops, vehicleType]);

  const handleStopsChange = useCallback((newStops) => {
    setStops(newStops);
  }, []);

  // Amenity discovery handlers
  const handleAmenityTypesChange = useCallback((types) => {
    setSelectedAmenityTypes(types);
  }, []);

  const handleAmenityAdd = useCallback((amenity) => {
    const amenityStop = {
      id: `amenity_${amenity.id}`,
      name: amenity.name,
      lat: amenity.lat,
      lng: amenity.lng,
      stop_type: 'amenity',
      category: amenity.category,
      order: stops.length
    };
    setStops(prev => [...prev, amenityStop]);
  }, [stops.length]);

  // Map interaction handlers
  const handlePinAdd = useCallback((pin) => {
    setCustomPins(prev => [...prev, pin]);
  }, []);

  const handlePinUpdate = useCallback((pin) => {
    setCustomPins(prev => prev.map(p => p.id === pin.id ? pin : p));
  }, []);

  const handlePinDelete = useCallback((pinId) => {
    setCustomPins(prev => prev.filter(p => p.id !== pinId));
  }, []);

  const handleLocationSelect = useCallback((location) => {
    // Handle location selection from search or landmarks
    handleStopAdd({
      name: location.name,
      lat: location.lat,
      lng: location.lng,
      stop_type: 'waypoint'
    });
  }, [handleStopAdd]);

  const handleSearchResults = useCallback((results) => {
    setSearchResults(results);
  }, []);

  // Quick action handlers for mobile FAB
  const handleQuickAction = useCallback((action) => {
    switch (action) {
      case 'add-stop':
        setActiveTab(0); // Switch to stops tab
        break;
      case 'search':
        setActiveTab(1); // Switch to amenities tab
        break;
      case 'calculate':
        handleCalculateEnhancedRoute();
        break;
      default:
        break;
    }
  }, [handleCalculateEnhancedRoute]);

  // Calculate route coordinates for amenity discovery
  const routeCoordinates = optimizedRoute?.coordinates || routePaths[0]?.coordinates || [];

  // Sidebar content with tabs
  const sidebarContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Stops" />
          <Tab label="Amenities" />
          <Tab label="Settings" />
        </Tabs>
      </Box>

      {activeTab === 0 && (
        <StopManagementPanel
          stops={stops}
          onStopsChange={handleStopsChange}
          onOptimizeRoute={handleOptimizeRoute}
          onAddStop={handleStopAdd}
          onCalculateRoute={handleCalculateEnhancedRoute}
          isCalculating={isCalculatingRoute}
        />
      )}

      {activeTab === 1 && (
        <AmenityDiscovery
          routeCoordinates={routeCoordinates}
          onAddAmenityToRoute={handleAmenityAdd}
          selectedAmenityTypes={selectedAmenityTypes}
          onAmenityTypesChange={handleAmenityTypesChange}
        />
      )}

      {activeTab === 2 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Route Settings
          </Typography>

          <Stack spacing={3}>
            <FormControl fullWidth>
              <InputLabel>Vehicle Type</InputLabel>
              <Select
                value={vehicleType}
                onChange={(e) => setVehicleType(e.target.value)}
                label="Vehicle Type"
              >
                <MenuItem value="car">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DirectionsCarIcon />
                    Car
                  </Box>
                </MenuItem>
                <MenuItem value="bike">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DirectionsBikeIcon />
                    Bike
                  </Box>
                </MenuItem>
                <MenuItem value="walk">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DirectionsWalkIcon />
                    Walk
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            <LocationSearch
              onSearchResults={handleSearchResults}
              landmarks={landmarks}
              placeholder="Search for locations..."
              showCategories={true}
            />

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            {optimizedRoute && (
              <Card sx={{ mt: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Route Summary
                  </Typography>
                  <Stack spacing={1}>
                    <Typography variant="body2">
                      Distance: {(optimizedRoute.total_distance || 0).toFixed(1)} km
                    </Typography>
                    <Typography variant="body2">
                      Time: {Math.round((optimizedRoute.total_time || 0) / 60)} minutes
                    </Typography>
                    <Typography variant="body2">
                      Stops: {stops.length}
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            )}
          </Stack>
        </Box>
      )}
    </Box>
  );

  return (
    <ThemeProvider>
      <EnhancedLayout
        title="Enhanced Route Planner"
        sidebarContent={sidebarContent}
        onQuickAction={handleQuickAction}
        showQuickActions={true}
      >
        <InteractiveMap
          center={mapCenter}
          zoom={mapZoom}
          landmarks={landmarks}
          customPins={customPins}
          searchResults={searchResults}
          stops={stops}
          amenities={amenities}
          routePaths={routePaths}
          onPinAdd={handlePinAdd}
          onPinUpdate={handlePinUpdate}
          onPinDelete={handlePinDelete}
          onLocationSelect={handleLocationSelect}
          onStopAdd={handleStopAdd}
          onStopEdit={handleStopEdit}
          onStopDelete={handleStopDelete}
          onStopSelect={handleStopSelect}
          onAmenityAdd={handleAmenityAdd}
          selectedStopId={selectedStopId}
          mapRef={mapRef}
          height="100%"
        />
      </EnhancedLayout>
    </ThemeProvider>
  );
};

export default EnhancedFindRoute;
