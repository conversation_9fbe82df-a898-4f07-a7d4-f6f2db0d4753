import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typo<PERSON>,
  IconButton,
  useMediaQuery,
  useTheme,
  Fab,
  Zoom,
  Backdrop,
  Paper,
  Stack,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Add as AddIcon,
  Search as SearchIcon,
  Route as RouteIcon
} from '@mui/icons-material';
import { useTheme as useAppTheme } from '../theme/ThemeProvider';

const DRAWER_WIDTH = 400;
const COLLAPSED_DRAWER_WIDTH = 60;

const EnhancedLayout = ({
  children,
  sidebarContent,
  title = "Route Planner",
  showQuickActions = true,
  onQuickAction,
  sidebarCollapsible = true,
  defaultSidebarOpen = true
}) => {
  const theme = useTheme();
  const { mode, toggleTheme, isDark } = useAppTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  const [sidebarOpen, setSidebarOpen] = useState(defaultSidebarOpen && !isMobile);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showFab, setShowFab] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
      setSidebarCollapsed(false);
    } else if (isTablet && sidebarCollapsible) {
      setSidebarCollapsed(true);
    }
  }, [isMobile, isTablet, sidebarCollapsible]);

  // Show FAB when sidebar is closed on mobile
  useEffect(() => {
    setShowFab(isMobile && !sidebarOpen);
  }, [isMobile, sidebarOpen]);

  const handleSidebarToggle = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else if (sidebarCollapsible) {
      setSidebarCollapsed(!sidebarCollapsed);
    } else {
      setSidebarOpen(!sidebarOpen);
    }
  };

  const handleSidebarClose = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const getDrawerWidth = () => {
    if (isMobile) return DRAWER_WIDTH;
    if (sidebarCollapsed) return COLLAPSED_DRAWER_WIDTH;
    return DRAWER_WIDTH;
  };

  const quickActions = [
    { icon: <AddIcon />, label: 'Add Stop', action: 'add-stop' },
    { icon: <SearchIcon />, label: 'Search', action: 'search' },
    { icon: <RouteIcon />, label: 'Calculate', action: 'calculate' },
  ];

  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          background: isDark 
            ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
            : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backdropFilter: 'blur(10px)',
          borderBottom: `1px solid ${theme.palette.divider}`,
          boxShadow: isDark 
            ? '0 4px 20px rgba(0, 0, 0, 0.3)'
            : '0 4px 20px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="toggle sidebar"
            onClick={handleSidebarToggle}
            edge="start"
            sx={{
              mr: 2,
              transition: 'transform 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)',
              },
            }}
          >
            {sidebarOpen || !sidebarCollapsed ? <ChevronLeftIcon /> : <MenuIcon />}
          </IconButton>
          
          <Typography 
            variant="h6" 
            noWrap 
            component="div" 
            sx={{ 
              flexGrow: 1,
              fontWeight: 600,
              background: 'linear-gradient(45deg, #fff 30%, #f0f0f0 90%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            {title}
          </Typography>

          <Tooltip title={`Switch to ${isDark ? 'light' : 'dark'} mode`}>
            <IconButton
              color="inherit"
              onClick={toggleTheme}
              sx={{
                transition: 'transform 0.2s ease-in-out',
                '&:hover': {
                  transform: 'rotate(180deg)',
                },
              }}
            >
              {isDark ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'persistent'}
        anchor="left"
        open={sidebarOpen}
        onClose={handleSidebarClose}
        sx={{
          width: getDrawerWidth(),
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: getDrawerWidth(),
            boxSizing: 'border-box',
            background: isDark 
              ? 'linear-gradient(180deg, #1e1e1e 0%, #2d2d2d 100%)'
              : 'linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%)',
            backdropFilter: 'blur(10px)',
            borderRight: `1px solid ${theme.palette.divider}`,
            transition: theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
            overflowX: 'hidden',
          },
        }}
        ModalProps={{
          keepMounted: true, // Better mobile performance
        }}
      >
        <Toolbar />
        
        {/* Sidebar Header */}
        {!sidebarCollapsed && (
          <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Typography variant="h6" color="text.primary" fontWeight={600}>
                Controls
              </Typography>
              {!isMobile && sidebarCollapsible && (
                <IconButton
                  onClick={() => setSidebarCollapsed(true)}
                  size="small"
                  sx={{
                    transition: 'transform 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'scale(1.1)',
                    },
                  }}
                >
                  <ChevronLeftIcon />
                </IconButton>
              )}
            </Stack>
          </Box>
        )}

        {/* Collapsed Sidebar Actions */}
        {sidebarCollapsed && !isMobile && (
          <Box sx={{ p: 1 }}>
            <Stack spacing={1} alignItems="center">
              <Tooltip title="Expand" placement="right">
                <IconButton
                  onClick={() => setSidebarCollapsed(false)}
                  sx={{
                    transition: 'transform 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'scale(1.1)',
                    },
                  }}
                >
                  <ChevronRightIcon />
                </IconButton>
              </Tooltip>
              <Divider sx={{ width: '100%' }} />
              {quickActions.map((action, index) => (
                <Tooltip key={index} title={action.label} placement="right">
                  <IconButton
                    onClick={() => onQuickAction && onQuickAction(action.action)}
                    sx={{
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'scale(1.1)',
                        backgroundColor: theme.palette.primary.main + '20',
                      },
                    }}
                  >
                    {action.icon}
                  </IconButton>
                </Tooltip>
              ))}
            </Stack>
          </Box>
        )}

        {/* Sidebar Content */}
        {!sidebarCollapsed && (
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            {sidebarContent}
          </Box>
        )}
      </Drawer>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          height: '100vh',
          overflow: 'hidden',
          transition: theme.transitions.create('margin', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          marginLeft: isMobile ? 0 : sidebarOpen ? 0 : `-${getDrawerWidth()}px`,
          background: isDark 
            ? 'linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%)'
            : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        }}
      >
        <Toolbar />
        <Box sx={{ height: 'calc(100vh - 64px)', overflow: 'hidden' }}>
          {children}
        </Box>
      </Box>

      {/* Mobile FAB */}
      {isMobile && showQuickActions && (
        <Box sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
          <Stack spacing={1} alignItems="flex-end">
            {quickActions.map((action, index) => (
              <Zoom
                key={index}
                in={showFab}
                timeout={200 + index * 100}
                style={{ transitionDelay: showFab ? `${index * 100}ms` : '0ms' }}
              >
                <Fab
                  size="small"
                  color="primary"
                  onClick={() => onQuickAction && onQuickAction(action.action)}
                  sx={{
                    background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
                    },
                  }}
                >
                  {action.icon}
                </Fab>
              </Zoom>
            ))}
            
            <Zoom in={showFab} timeout={200}>
              <Fab
                color="primary"
                onClick={handleSidebarToggle}
                sx={{
                  background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
                  },
                }}
              >
                <MenuIcon />
              </Fab>
            </Zoom>
          </Stack>
        </Box>
      )}

      {/* Mobile Backdrop */}
      {isMobile && (
        <Backdrop
          open={sidebarOpen}
          onClick={handleSidebarClose}
          sx={{ zIndex: theme.zIndex.drawer - 1 }}
        />
      )}
    </Box>
  );
};

export default EnhancedLayout;
