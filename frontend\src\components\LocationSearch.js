import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Autocomplete,
  Chip,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Alert,
  Paper,
  Grid,
  Button
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Restaurant as RestaurantIcon,
  LocalHospital as LocalHospitalIcon,
  School as SchoolIcon,
  Hotel as HotelIcon,
  LocalGasStation as LocalGasStationIcon,
  ShoppingCart as ShoppingCartIcon,
  AccountBalance as AccountBalanceIcon,
  Place as PlaceIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const categoryIcons = {
  restaurant: <RestaurantIcon />,
  food: <RestaurantIcon />,
  hospital: <LocalHospitalIcon />,
  medical: <LocalHospitalIcon />,
  school: <SchoolIcon />,
  education: <SchoolIcon />,
  hotel: <HotelIcon />,
  accommodation: <HotelIcon />,
  gas_station: <LocalGasStationIcon />,
  fuel: <LocalGasStationIcon />,
  shopping: <ShoppingCartIcon />,
  market: <ShoppingCartIcon />,
  government: <AccountBalanceIcon />,
  public_service: <AccountBalanceIcon />,
  bank: <AccountBalanceIcon />,
  atm: <AccountBalanceIcon />,
  pharmacy: <LocalHospitalIcon />,
  temple: <PlaceIcon />,
  worship: <PlaceIcon />
};

const LocationSearch = ({ 
  onSearchResults, 
  onCategoryFilter, 
  landmarks = [],
  placeholder = "Search for places...",
  showCategories = true 
}) => {
  const { api } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchOptions, setSearchOptions] = useState([]);

  // Fetch available categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await api.get('/search/categories');
        setCategories(response.data.categories);
      } catch (err) {
        console.error('Failed to fetch categories:', err);
      }
    };

    if (showCategories) {
      fetchCategories();
    }
  }, [api, showCategories]);

  // Combine landmarks with search results for autocomplete
  useEffect(() => {
    const landmarkOptions = landmarks.map(landmark => ({
      ...landmark,
      label: landmark.name,
      type: 'landmark'
    }));

    const searchOptions = searchResults.map(result => ({
      ...result,
      label: result.name,
      type: 'search'
    }));

    setSearchOptions([...landmarkOptions, ...searchOptions]);
  }, [landmarks, searchResults]);

  const handleSearch = async (query, category = null) => {
    if (!query && !category) return;

    setLoading(true);
    setError('');

    try {
      const response = await api.post('/search/places', {
        query: query || '',
        place_type: category,
        limit: 20
      });

      const results = response.data.results || [];
      setSearchResults(results);
      onSearchResults && onSearchResults(results);
    } catch (err) {
      setError('Search failed. Please try again.');
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
    setAnchorEl(null);
    handleSearch(searchQuery, category.id);
    onCategoryFilter && onCategoryFilter(category);
  };

  const handleClearCategory = () => {
    setSelectedCategory(null);
    if (searchQuery) {
      handleSearch(searchQuery, null);
    } else {
      setSearchResults([]);
      onSearchResults && onSearchResults([]);
    }
  };

  const handleInputChange = (event, newValue) => {
    setSearchQuery(newValue);
    
    // Debounced search
    if (newValue.length > 2) {
      const timeoutId = setTimeout(() => {
        handleSearch(newValue, selectedCategory?.id);
      }, 500);
      
      return () => clearTimeout(timeoutId);
    }
  };

  const handleOptionSelect = (event, value) => {
    if (value) {
      // Handle selection of a specific location
      onSearchResults && onSearchResults([value]);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mb: 2 }}>
        <Autocomplete
          freeSolo
          fullWidth
          options={searchOptions}
          getOptionLabel={(option) => typeof option === 'string' ? option : option.label}
          renderOption={(props, option) => (
            <Box component="li" {...props}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {categoryIcons[option.category] || <PlaceIcon />}
                <Box>
                  <Typography variant="body2">{option.name}</Typography>
                  <Typography variant="caption" color="textSecondary">
                    {option.type === 'landmark' ? 'Landmark' : option.category}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder={placeholder}
              InputProps={{
                ...params.InputProps,
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                endAdornment: loading ? <CircularProgress size={20} /> : params.InputProps.endAdornment,
              }}
            />
          )}
          onInputChange={handleInputChange}
          onChange={handleOptionSelect}
          loading={loading}
        />

        {showCategories && (
          <IconButton
            onClick={(e) => setAnchorEl(e.currentTarget)}
            color={selectedCategory ? 'primary' : 'default'}
          >
            <FilterListIcon />
          </IconButton>
        )}
      </Box>

      {/* Selected category chip */}
      {selectedCategory && (
        <Box sx={{ mb: 2 }}>
          <Chip
            icon={categoryIcons[selectedCategory.id] || <PlaceIcon />}
            label={selectedCategory.name}
            onDelete={handleClearCategory}
            color="primary"
            variant="outlined"
          />
        </Box>
      )}

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Category filter menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        PaperProps={{
          sx: { maxHeight: 400, width: 250 }
        }}
      >
        <MenuItem onClick={() => setAnchorEl(null)}>
          <ListItemText primary="All Categories" />
        </MenuItem>
        {categories.map((category) => (
          <MenuItem
            key={category.id}
            onClick={() => handleCategorySelect(category)}
          >
            <ListItemIcon>
              {categoryIcons[category.id] || <PlaceIcon />}
            </ListItemIcon>
            <ListItemText primary={category.name} />
          </MenuItem>
        ))}
      </Menu>

      {/* Quick category buttons */}
      {showCategories && categories.length > 0 && (
        <Paper sx={{ p: 2, mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Quick Search
          </Typography>
          <Grid container spacing={1}>
            {categories.slice(0, 8).map((category) => (
              <Grid item key={category.id}>
                <Button
                  size="small"
                  variant={selectedCategory?.id === category.id ? 'contained' : 'outlined'}
                  startIcon={categoryIcons[category.id] || <PlaceIcon />}
                  onClick={() => handleCategorySelect(category)}
                >
                  {category.name}
                </Button>
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}
    </Box>
  );
};

export default LocationSearch;
