import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Stack,
  IconButton,
  Collapse,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  Tooltip,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Restaurant as RestaurantIcon,
  LocalGasStation as GasIcon,
  LocalHospital as HospitalIcon,
  LocalPharmacy as PharmacyIcon,
  Hotel as HotelIcon,
  School as SchoolIcon,
  ShoppingCart as ShoppingIcon,
  AccountBalance as BankIcon,
  ExpandMore as ExpandIcon,
  Add as AddIcon,
  DirectionsCar as CarIcon,
  AccessTime as TimeIcon,
  Place as PlaceIcon
} from '@mui/icons-material';
import axios from 'axios';

const AMENITY_CATEGORIES = [
  { id: 'restaurant', label: 'Restaurants', icon: <RestaurantIcon />, color: '#FF6B6B' },
  { id: 'gas_station', label: 'Gas Stations', icon: <GasIcon />, color: '#4ECDC4' },
  { id: 'hospital', label: 'Hospitals', icon: <HospitalIcon />, color: '#45B7D1' },
  { id: 'pharmacy', label: 'Pharmacies', icon: <PharmacyIcon />, color: '#96CEB4' },
  { id: 'hotel', label: 'Hotels', icon: <HotelIcon />, color: '#FFEAA7' },
  { id: 'school', label: 'Schools', icon: <SchoolIcon />, color: '#DDA0DD' },
  { id: 'shopping', label: 'Shopping', icon: <ShoppingIcon />, color: '#98D8C8' },
  { id: 'atm', label: 'ATMs/Banks', icon: <BankIcon />, color: '#F7DC6F' }
];

const AmenityDiscovery = ({
  routeCoordinates = [],
  onAddAmenityToRoute,
  selectedAmenityTypes = [],
  onAmenityTypesChange
}) => {
  const [amenities, setAmenities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedAmenity, setExpandedAmenity] = useState(null);
  const [searchSettings, setSearchSettings] = useState({
    bufferDistance: 500,
    maxDetourDistance: 2000
  });

  useEffect(() => {
    if (routeCoordinates.length > 0 && selectedAmenityTypes.length > 0) {
      searchAmenities();
    }
  }, [routeCoordinates, selectedAmenityTypes]);

  const searchAmenities = async () => {
    if (!routeCoordinates.length || !selectedAmenityTypes.length) return;

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        'http://localhost:8000/routes/find-amenities',
        {
          route_coordinates: routeCoordinates,
          amenity_types: selectedAmenityTypes,
          buffer_distance: searchSettings.bufferDistance,
          max_detour_distance: searchSettings.maxDetourDistance,
          max_results: 20
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      setAmenities(response.data.amenities || []);
    } catch (err) {
      console.error('Error searching amenities:', err);
      setError('Failed to search for amenities. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAmenityTypeToggle = (event, newTypes) => {
    onAmenityTypesChange(newTypes || []);
  };

  const formatDistance = (meters) => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    }
    return `${(meters / 1000).toFixed(1)}km`;
  };

  const formatTime = (seconds) => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.round(seconds / 60);
    return `${minutes}min`;
  };

  const getCategoryInfo = (categoryId) => {
    return AMENITY_CATEGORIES.find(cat => cat.id === categoryId) || 
           { label: categoryId, icon: <PlaceIcon />, color: '#666' };
  };

  const handleAddToRoute = (amenity) => {
    const stop = {
      id: `amenity_${amenity.id}`,
      name: amenity.name,
      lat: amenity.lat,
      lng: amenity.lng,
      stop_type: 'amenity',
      category: amenity.category,
      order: 999 // Will be reordered
    };
    onAddAmenityToRoute(stop);
  };

  const groupedAmenities = amenities.reduce((groups, amenity) => {
    const category = amenity.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(amenity);
    return groups;
  }, {});

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom>
          Route Amenities
        </Typography>
        
        {/* Amenity Type Selection */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Select amenity types to find along your route:
        </Typography>
        
        <ToggleButtonGroup
          value={selectedAmenityTypes}
          onChange={handleAmenityTypeToggle}
          aria-label="amenity types"
          size="small"
          sx={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: 0.5,
            '& .MuiToggleButton-root': {
              border: 1,
              borderRadius: 1,
              m: 0.25,
              minWidth: 'auto'
            }
          }}
        >
          {AMENITY_CATEGORIES.map((category) => (
            <ToggleButton
              key={category.id}
              value={category.id}
              aria-label={category.label}
              sx={{
                color: category.color,
                '&.Mui-selected': {
                  backgroundColor: `${category.color}20`,
                  color: category.color,
                  '&:hover': {
                    backgroundColor: `${category.color}30`,
                  }
                }
              }}
            >
              <Tooltip title={category.label}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {category.icon}
                  <Typography variant="caption" sx={{ display: { xs: 'none', sm: 'block' } }}>
                    {category.label}
                  </Typography>
                </Box>
              </Tooltip>
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {!loading && !error && routeCoordinates.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <PlaceIcon sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="body2">
              Calculate a route first to discover amenities
            </Typography>
          </Box>
        )}

        {!loading && !error && routeCoordinates.length > 0 && selectedAmenityTypes.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <RestaurantIcon sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="body2">
              Select amenity types above to start discovering
            </Typography>
          </Box>
        )}

        {!loading && !error && amenities.length === 0 && selectedAmenityTypes.length > 0 && routeCoordinates.length > 0 && (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <PlaceIcon sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="body2">
              No amenities found along your route
            </Typography>
            <Typography variant="caption">
              Try increasing the search distance or selecting different types
            </Typography>
          </Box>
        )}

        {/* Amenities by Category */}
        {Object.entries(groupedAmenities).map(([category, categoryAmenities]) => {
          const categoryInfo = getCategoryInfo(category);
          
          return (
            <Box key={category} sx={{ mb: 2 }}>
              <Typography 
                variant="subtitle2" 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1, 
                  mb: 1,
                  color: categoryInfo.color,
                  fontWeight: 600
                }}
              >
                {categoryInfo.icon}
                {categoryInfo.label} ({categoryAmenities.length})
              </Typography>

              {categoryAmenities.map((amenity) => (
                <Card key={amenity.id} sx={{ mb: 1, border: 1, borderColor: 'divider' }}>
                  <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography variant="body2" fontWeight={500} noWrap>
                          {amenity.name}
                        </Typography>
                        
                        <Stack direction="row" spacing={1} sx={{ mt: 0.5 }}>
                          <Chip
                            size="small"
                            icon={<PlaceIcon />}
                            label={formatDistance(amenity.distance_from_route)}
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                          
                          {amenity.estimated_detour_time > 0 && (
                            <Chip
                              size="small"
                              icon={<TimeIcon />}
                              label={`+${formatTime(amenity.estimated_detour_time)}`}
                              color="warning"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                          )}
                          
                          {amenity.distance_from_route <= 100 && (
                            <Chip
                              size="small"
                              label="On Route"
                              color="success"
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                          )}
                        </Stack>
                      </Box>

                      <Button
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={() => handleAddToRoute(amenity)}
                        variant="outlined"
                        sx={{ ml: 1, flexShrink: 0 }}
                      >
                        Add
                      </Button>
                    </Stack>

                    {/* Expandable Details */}
                    {amenity.details && (
                      <>
                        <IconButton
                          size="small"
                          onClick={() => setExpandedAmenity(
                            expandedAmenity === amenity.id ? null : amenity.id
                          )}
                          sx={{ mt: 1, p: 0 }}
                        >
                          <ExpandIcon 
                            sx={{ 
                              transform: expandedAmenity === amenity.id ? 'rotate(180deg)' : 'rotate(0deg)',
                              transition: 'transform 0.2s'
                            }} 
                          />
                        </IconButton>
                        
                        <Collapse in={expandedAmenity === amenity.id}>
                          <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider' }}>
                            <Grid container spacing={1}>
                              {amenity.details.opening_hours && (
                                <Grid item xs={12}>
                                  <Typography variant="caption" color="text.secondary">
                                    Hours: {amenity.details.opening_hours}
                                  </Typography>
                                </Grid>
                              )}
                              {amenity.details.phone && (
                                <Grid item xs={12}>
                                  <Typography variant="caption" color="text.secondary">
                                    Phone: {amenity.details.phone}
                                  </Typography>
                                </Grid>
                              )}
                              {amenity.details.address && (
                                <Grid item xs={12}>
                                  <Typography variant="caption" color="text.secondary">
                                    Address: {[
                                      amenity.details.address.street,
                                      amenity.details.address.city,
                                      amenity.details.address.postcode
                                    ].filter(Boolean).join(', ')}
                                  </Typography>
                                </Grid>
                              )}
                            </Grid>
                          </Box>
                        </Collapse>
                      </>
                    )}
                  </CardContent>
                </Card>
              ))}
            </Box>
          );
        })}
      </Box>

      {/* Summary */}
      {amenities.length > 0 && (
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
          <Typography variant="caption" color="text.secondary">
            Found {amenities.length} amenities • 
            Within {formatDistance(searchSettings.bufferDistance)} of route • 
            Max {formatDistance(searchSettings.maxDetourDistance)} detour
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default AmenityDiscovery;
