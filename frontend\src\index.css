body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', '<PERSON><PERSON>', 'Oxy<PERSON>',
    '<PERSON>bunt<PERSON>', 'Can<PERSON><PERSON>', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f8fafc;
  min-height: 100vh;
  color: #1e293b;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Clean, modern styling */
* {
  box-sizing: border-box;
}

/* Smooth transitions */
a, button, .MuiButtonBase-root {
  transition: all 0.2s ease-in-out !important;
}

/* Clean card styling */
.MuiCard-root {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease-in-out;
}

.MuiCard-root:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transform: translateY(-1px);
}

/* Clean map styling */
.leaflet-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

/* Clean form controls */
.MuiOutlinedInput-root {
  background: #ffffff;
}

.MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
  border-color: #d1d5db;
}

.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #6366f1;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #6366f1;
  border-width: 2px;
}

/* Clean paper components */
.MuiPaper-root {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}
