body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', '<PERSON><PERSON>', 'Oxy<PERSON>',
    '<PERSON>bu<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f8fafc;
  min-height: 100vh;
  color: #1e293b;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Clean, modern styling */
* {
  box-sizing: border-box;
}

/* Smooth transitions */
a, button, .MuiButtonBase-root {
  transition: all 0.2s ease-in-out !important;
}

/* Clean card styling */
.MuiCard-root {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease-in-out;
}

.MuiCard-root:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transform: translateY(-1px);
}

/* Enhanced map styling */
.leaflet-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  font-family: 'Inter', sans-serif;
  width: 100% !important;
  height: 100% !important;
}

/* Fix map tile loading issues */
.leaflet-tile-container {
  width: 100% !important;
  height: 100% !important;
}

.leaflet-tile {
  max-width: none !important;
  max-height: none !important;
}

/* Ensure map controls are properly styled */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.leaflet-control-zoom a {
  background-color: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  color: #374151 !important;
  font-weight: 600 !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb !important;
  border-color: #6366f1 !important;
}

/* Fix attribution styling */
.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 4px !important;
  font-size: 11px !important;
}

/* Improve popup styling */
.leaflet-popup-content-wrapper {
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e2e8f0 !important;
}

.leaflet-popup-tip {
  border-top-color: #ffffff !important;
}

/* Custom numbered markers */
.custom-numbered-marker {
  background: transparent !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.custom-numbered-marker svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Ensure markers are properly displayed */
.leaflet-marker-icon {
  border-radius: 50% 50% 50% 0 !important;
  transform: rotate(-45deg) !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Clean form controls */
.MuiOutlinedInput-root {
  background: #ffffff;
}

.MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
  border-color: #d1d5db;
}

.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #6366f1;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #6366f1;
  border-width: 2px;
}

/* Clean paper components */
.MuiPaper-root {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}
