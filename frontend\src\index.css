body {
  margin: 0;
  font-family: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f7;
  color: #333;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Add smooth transitions to all interactive elements */
a, button, .MuiButtonBase-root {
  transition: all 0.2s ease-in-out !important;
}

/* Add hover effects to cards */
.MuiCard-root {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.MuiCard-root:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12) !important;
}

/* Add cool map styling */
.leaflet-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Enhance form controls */
.MuiOutlinedInput-root {
  border-radius: 8px;
  overflow: hidden;
}
