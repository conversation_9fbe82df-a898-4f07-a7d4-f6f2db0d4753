from typing import List, Dict, Any, Optional
import requests
import time

# Dehradun city bounds for strict filtering
DEHRADUN_BOUNDS = {
    "south": 30.25,
    "west": 77.95,
    "north": 30.45,
    "east": 78.15
}

def is_within_dehradun_bounds(lat: float, lng: float) -> bool:
    """Check if coordinates are within Dehradun city bounds"""
    return (DEHRADUN_BOUNDS["south"] <= lat <= DEHRADUN_BOUNDS["north"] and
            DEHRADUN_BOUNDS["west"] <= lng <= DEHRADUN_BOUNDS["east"])

def filter_dehradun_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Filter results to only include locations within Dehradun bounds"""
    filtered_results = []
    for result in results:
        try:
            lat = float(result.get("lat", 0))
            lng = float(result.get("lon", 0))

            # Check if within bounds
            if is_within_dehradun_bounds(lat, lng):
                # Additional filtering for Dehradun-specific addresses
                display_name = result.get("display_name", "").lower()
                address = result.get("address", {})

                # Prefer results that mention Dehradun, Uttarakhand, or known Dehradun areas
                dehradun_keywords = ["dehradun", "uttarakhand", "doon", "mussoorie road", "rajpur road", "clock tower"]
                if any(keyword in display_name for keyword in dehradun_keywords) or \
                   address.get("city", "").lower() == "dehradun" or \
                   address.get("state", "").lower() == "uttarakhand":
                    filtered_results.append(result)
        except (ValueError, TypeError):
            continue

    return filtered_results

DEHRADUN_LOCATIONS = sorted([
    {"name": "Ballupur", "lat": 30.333275, "lng": 78.011248, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Badripur", "lat": 30.284644, "lng": 78.065020, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Vasant Vihar", "lat": 30.323023, "lng": 78.004126, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Bharuwala Grant", "lat": 30.2675, "lng": 77.9959, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Chakrata Road", "lat": 30.3456, "lng": 78.0112, "type": "transport", "parking": True, "traffic_zone": "medium"},
    {"name": "Clement Town", "lat": 30.2791, "lng": 78.0078, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Clock Tower", "lat": 30.3242, "lng": 78.0417, "type": "commercial", "parking": True, "traffic_zone": "high"},
    {"name": "Dalanwala", "lat": 30.3126, "lng": 78.0573, "type": "commercial", "parking": False, "traffic_zone": "high"},
    {"name": "Doiwala", "lat": 30.1758, "lng": 78.1242, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Doon University", "lat": 30.2697, "lng": 78.0436, "type": "institutional", "parking": True, "traffic_zone": "low"},
    {"name": "Forest Research Institute", "lat": 30.3421, "lng": 77.9972, "type": "institutional", "parking": True, "traffic_zone": "low"},
    {"name": "Graphic Era University", "lat": 30.268745, "lng": 77.993425, "type": "institutional", "parking": True, "traffic_zone": "medium"},
    {"name": "Harrawala", "lat": 30.2507, "lng": 78.0772, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "ISBT Dehradun", "lat": 30.2879, "lng": 77.9985, "type": "transport", "parking": True, "traffic_zone": "high"},
    {"name": "Jolly Grant Airport", "lat": 30.1872, "lng": 78.1748, "type": "transport", "parking": True, "traffic_zone": "low"},
    {"name": "Kandoli", "lat": 30.3599, "lng": 78.0624, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Kargi Chowk", "lat": 30.290801, "lng": 78.024759, "type": "commercial", "parking": True, "traffic_zone": "medium"},
    {"name": "Kedarpur", "lat": 30.3123, "lng": 78.0456, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Majra", "lat": 30.2947, "lng": 77.9937, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Mothrowala", "lat": 30.2679, "lng": 78.0368, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Mussoorie Diversion", "lat": 30.371537, "lng": 78.077424, "type": "transport", "parking": True, "traffic_zone": "medium"},
    {"name": "Nehru Colony", "lat": 30.2986, "lng": 78.0555, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Pacific Hills", "lat": 30.3486, "lng": 78.0344, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Paltan Bazaar", "lat": 30.3222, "lng": 78.0373, "type": "commercial", "parking": False, "traffic_zone": "high"},
    {"name": "Patel Nagar", "lat": 30.3210, "lng": 78.0215, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Premnagar", "lat": 30.3350, "lng": 77.9582, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Race Course", "lat": 30.3145, "lng": 78.0438, "type": "recreational", "parking": True, "traffic_zone": "low"},
    {"name": "Rajpur", "lat": 30.3848, "lng": 78.0950, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Rajpur Road", "lat": 30.3346, "lng": 78.0504, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Raipur", "lat": 30.3253, "lng": 78.0802, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Robbers Cave", "lat": 30.3758, "lng": 78.0841, "type": "recreational", "parking": True, "traffic_zone": "low"},
    {"name": "Sahastradhara", "lat": 30.3873, "lng": 78.1268, "type": "recreational", "parking": True, "traffic_zone": "medium"},
    {"name": "Selaqui", "lat": 30.366160, "lng": 77.858086, "type": "industrial", "parking": True, "traffic_zone": "low"},
    {"name": "Sahastradhara Road", "lat": 30.358394, "lng": 78.088158, "type": "transport", "parking": True, "traffic_zone": "medium"},
    {"name": "Subhash Nagar", "lat": 30.2733, "lng": 77.9926, "type": "residential", "parking": True, "traffic_zone": "medium"},
    {"name": "Survey Chowk", "lat": 30.3259, "lng": 78.0470, "type": "commercial", "parking": False, "traffic_zone": "high"},
    {"name": "Tapovan", "lat": 30.3382, "lng": 78.0801, "type": "residential", "parking": True, "traffic_zone": "low"},
    {"name": "Dharampur", "lat": 30.2991, "lng": 78.0571, "type": "residential", "parking": True, "traffic_zone": "medium"},
], key=lambda x: x["name"])

def get_all_locations() -> List[Dict[str, Any]]:
    return DEHRADUN_LOCATIONS

def get_location_by_name(name: str) -> Dict[str, Any]:
    return next((loc for loc in DEHRADUN_LOCATIONS if loc["name"] == name), None)

def geocode_location(query: str, bounds: Optional[Dict] = None) -> List[Dict[str, Any]]:
    """
    Geocode a location using OpenStreetMap Nominatim API
    """
    base_url = "https://nominatim.openstreetmap.org/search"

    # Tighter bounds for Dehradun city and immediate surroundings
    if bounds is None:
        bounds = {
            "south": 30.25,   # More restrictive southern boundary
            "west": 77.95,    # More restrictive western boundary
            "north": 30.45,   # More restrictive northern boundary
            "east": 78.15     # More restrictive eastern boundary
        }

    params = {
        "q": query,
        "format": "json",
        "limit": 10,
        "bounded": 1,
        "viewbox": f"{bounds['west']},{bounds['north']},{bounds['east']},{bounds['south']}",
        "addressdetails": 1,
        "extratags": 1,
        "countrycodes": "in",  # Restrict to India only
        "city": "dehradun"     # Prefer Dehradun city results
    }

    try:
        response = requests.get(base_url, params=params, timeout=10)
        if response.status_code == 200:
            results = response.json()
            # Filter results to only include Dehradun area locations
            filtered_raw_results = filter_dehradun_results(results)
            formatted_results = []

            for result in filtered_raw_results:
                formatted_result = {
                    "name": result.get("display_name", "Unknown"),
                    "lat": float(result.get("lat", 0)),
                    "lng": float(result.get("lon", 0)),
                    "type": result.get("type", "unknown"),
                    "category": result.get("class", "unknown"),
                    "address": result.get("display_name", ""),
                    "place_id": result.get("place_id"),
                    "importance": result.get("importance", 0)
                }
                formatted_results.append(formatted_result)

            return formatted_results
        else:
            print(f"Geocoding error: {response.status_code}")
            return []
    except Exception as e:
        print(f"Geocoding request failed: {e}")
        return []

def reverse_geocode(lat: float, lng: float) -> Dict[str, Any]:
    """
    Reverse geocode coordinates to get location information
    """
    base_url = "https://nominatim.openstreetmap.org/reverse"

    params = {
        "lat": lat,
        "lon": lng,
        "format": "json",
        "addressdetails": 1,
        "extratags": 1
    }

    try:
        response = requests.get(base_url, params=params, timeout=10)
        if response.status_code == 200:
            result = response.json()

            address = result.get("address", {})
            formatted_result = {
                "name": result.get("display_name", f"Location at {lat:.4f}, {lng:.4f}"),
                "lat": lat,
                "lng": lng,
                "type": result.get("type", "unknown"),
                "category": result.get("class", "unknown"),
                "address": result.get("display_name", ""),
                "place_id": result.get("place_id"),
                "city": address.get("city", address.get("town", address.get("village", ""))),
                "state": address.get("state", ""),
                "country": address.get("country", ""),
                "postcode": address.get("postcode", "")
            }

            return formatted_result
        else:
            print(f"Reverse geocoding error: {response.status_code}")
            return {
                "name": f"Location at {lat:.4f}, {lng:.4f}",
                "lat": lat,
                "lng": lng,
                "type": "custom",
                "category": "point",
                "address": f"Coordinates: {lat:.4f}, {lng:.4f}"
            }
    except Exception as e:
        print(f"Reverse geocoding request failed: {e}")
        return {
            "name": f"Location at {lat:.4f}, {lng:.4f}",
            "lat": lat,
            "lng": lng,
            "type": "custom",
            "category": "point",
            "address": f"Coordinates: {lat:.4f}, {lng:.4f}"
        }

def search_places_by_type(place_type: str, bounds: Optional[Dict] = None, limit: int = 20) -> List[Dict[str, Any]]:
    """
    Search for places by type using OpenStreetMap Nominatim API
    """
    base_url = "https://nominatim.openstreetmap.org/search"

    # Tighter bounds for Dehradun city and immediate surroundings
    if bounds is None:
        bounds = {
            "south": 30.25,   # More restrictive southern boundary
            "west": 77.95,    # More restrictive western boundary
            "north": 30.45,   # More restrictive northern boundary
            "east": 78.15     # More restrictive eastern boundary
        }

    # Map place types to Nominatim amenity types
    type_mapping = {
        "restaurant": "restaurant",
        "food": "restaurant,fast_food,cafe,food_court",
        "hospital": "hospital,clinic,doctors",
        "medical": "hospital,clinic,doctors,pharmacy",
        "school": "school,university,college",
        "education": "school,university,college,library",
        "hotel": "hotel,guest_house,hostel",
        "accommodation": "hotel,guest_house,hostel,motel",
        "gas_station": "fuel",
        "fuel": "fuel",
        "shopping": "mall,marketplace,supermarket",
        "market": "marketplace,supermarket,mall",
        "government": "townhall,courthouse,police,fire_station",
        "public_service": "townhall,courthouse,police,fire_station,post_office",
        "bank": "bank,atm",
        "atm": "atm",
        "pharmacy": "pharmacy",
        "temple": "place_of_worship",
        "worship": "place_of_worship"
    }

    amenity_types = type_mapping.get(place_type.lower(), place_type)

    results = []

    # Search for each amenity type
    for amenity in amenity_types.split(","):
        params = {
            "amenity": amenity.strip(),
            "format": "json",
            "limit": limit,
            "bounded": 1,
            "viewbox": f"{bounds['west']},{bounds['north']},{bounds['east']},{bounds['south']}",
            "addressdetails": 1,
            "extratags": 1,
            "countrycodes": "in",  # Restrict to India only
            "city": "dehradun"     # Prefer Dehradun city results
        }

        try:
            # Add delay to respect rate limits
            time.sleep(0.1)

            response = requests.get(base_url, params=params, timeout=10)
            if response.status_code == 200:
                places = response.json()
                # Filter places to only include Dehradun area locations
                filtered_places = filter_dehradun_results(places)

                for place in filtered_places:
                    formatted_place = {
                        "name": place.get("display_name", "Unknown"),
                        "lat": float(place.get("lat", 0)),
                        "lng": float(place.get("lon", 0)),
                        "type": place.get("type", amenity),
                        "category": place.get("class", "amenity"),
                        "amenity": amenity,
                        "address": place.get("display_name", ""),
                        "place_id": place.get("place_id"),
                        "importance": place.get("importance", 0),
                        "tags": place.get("extratags", {})
                    }

                    # Extract more specific information from tags
                    tags = place.get("extratags", {})
                    if "name" in tags:
                        formatted_place["name"] = tags["name"]
                    if "phone" in tags:
                        formatted_place["phone"] = tags["phone"]
                    if "website" in tags:
                        formatted_place["website"] = tags["website"]
                    if "opening_hours" in tags:
                        formatted_place["opening_hours"] = tags["opening_hours"]

                    results.append(formatted_place)
            else:
                print(f"Place search error for {amenity}: {response.status_code}")
        except Exception as e:
            print(f"Place search request failed for {amenity}: {e}")

    # Remove duplicates based on place_id and sort by importance
    unique_results = {}
    for result in results:
        place_id = result.get("place_id")
        if place_id and place_id not in unique_results:
            unique_results[place_id] = result

    # Sort by importance (higher is better)
    sorted_results = sorted(unique_results.values(), key=lambda x: x.get("importance", 0), reverse=True)

    return sorted_results[:limit]