"""
Enhanced route service with advanced stop management and optimization
"""
import math
import requests
from typing import List, Dict, <PERSON>, Tu<PERSON>, Optional
from .route_service import get_osrm_route, decode_polyline


def calculate_distance(lat1: float, lng1: float, lat2: float, lng2: float) -> float:
    """Calculate distance between two points using Haversine formula"""
    R = 6371  # Earth's radius in kilometers
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lng = math.radians(lng2 - lng1)
    
    a = (math.sin(delta_lat / 2) ** 2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * 
         math.sin(delta_lng / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c


def get_route_between_points(start_lat: float, start_lng: float, 
                           end_lat: float, end_lng: float) -> Dict[str, Any]:
    """Get route information between two points using OSRM"""
    try:
        result = get_osrm_route(start_lng, start_lat, end_lng, end_lat)
        if result and result.get("routes"):
            route = result["routes"][0]
            return {
                "distance": route["distance"] / 1000.0,  # Convert to km
                "duration": route["duration"],  # In seconds
                "geometry": route["geometry"],
                "coordinates": decode_polyline(route["geometry"])
            }
    except Exception as e:
        print(f"Error getting route: {e}")
    
    # Fallback to straight-line distance
    distance = calculate_distance(start_lat, start_lng, end_lat, end_lng)
    return {
        "distance": distance,
        "duration": distance * 60,  # Rough estimate: 1 km per minute
        "geometry": None,
        "coordinates": [[start_lat, start_lng], [end_lat, end_lng]]
    }


def optimize_stop_sequence(stops: List[Dict], optimization_type: str = "time") -> List[Dict]:
    """
    Optimize the sequence of stops using a greedy nearest-neighbor approach
    This is a simplified TSP solution suitable for small numbers of stops
    """
    if len(stops) <= 2:
        return stops
    
    # Separate start and end points
    start_stop = None
    end_stop = None
    waypoints = []
    
    for stop in stops:
        if stop.get("stop_type") == "start":
            start_stop = stop
        elif stop.get("stop_type") == "end":
            end_stop = stop
        else:
            waypoints.append(stop)
    
    if not start_stop or not waypoints:
        return stops
    
    # Greedy nearest-neighbor optimization for waypoints
    optimized_waypoints = []
    remaining_waypoints = waypoints.copy()
    current_point = start_stop
    
    while remaining_waypoints:
        nearest_stop = None
        min_cost = float('inf')
        
        for waypoint in remaining_waypoints:
            if optimization_type == "time":
                route_info = get_route_between_points(
                    current_point["lat"], current_point["lng"],
                    waypoint["lat"], waypoint["lng"]
                )
                cost = route_info["duration"]
            else:  # distance
                cost = calculate_distance(
                    current_point["lat"], current_point["lng"],
                    waypoint["lat"], waypoint["lng"]
                )
            
            if cost < min_cost:
                min_cost = cost
                nearest_stop = waypoint
        
        if nearest_stop:
            optimized_waypoints.append(nearest_stop)
            remaining_waypoints.remove(nearest_stop)
            current_point = nearest_stop
    
    # Rebuild the stops list with optimized order
    result = [start_stop] + optimized_waypoints
    if end_stop:
        result.append(end_stop)
    
    # Update order numbers
    for i, stop in enumerate(result):
        stop["order"] = i
    
    return result


def calculate_route_with_stops(stops: List[Dict]) -> Dict[str, Any]:
    """Calculate a route through multiple stops"""
    if len(stops) < 2:
        raise ValueError("At least 2 stops required")
    
    total_distance = 0
    total_time = 0
    all_coordinates = []
    route_segments = []
    
    for i in range(len(stops) - 1):
        current_stop = stops[i]
        next_stop = stops[i + 1]
        
        route_info = get_route_between_points(
            current_stop["lat"], current_stop["lng"],
            next_stop["lat"], next_stop["lng"]
        )
        
        total_distance += route_info["distance"]
        total_time += route_info["duration"]
        
        # Add coordinates, avoiding duplicates at connection points
        if i == 0:
            all_coordinates.extend(route_info["coordinates"])
        else:
            all_coordinates.extend(route_info["coordinates"][1:])
        
        route_segments.append({
            "from": current_stop["name"],
            "to": next_stop["name"],
            "distance": route_info["distance"],
            "duration": route_info["duration"]
        })
    
    return {
        "total_distance": total_distance,
        "total_time": total_time,
        "coordinates": all_coordinates,
        "segments": route_segments,
        "geometry": {
            "type": "LineString",
            "coordinates": [[coord[1], coord[0]] for coord in all_coordinates]
        }
    }


def calculate_multi_stop_route(stops: List[Dict], optimize_order: bool = False, 
                             vehicle_type: str = "car") -> Dict[str, Any]:
    """
    Calculate a multi-stop route with optional optimization
    """
    if optimize_order:
        optimized_stops = optimize_stop_sequence(stops, "time")
    else:
        optimized_stops = stops
    
    route_result = calculate_route_with_stops(optimized_stops)
    
    # Calculate savings if optimization was used
    savings = None
    if optimize_order and len(stops) > 2:
        original_route = calculate_route_with_stops(stops)
        savings = {
            "distance_saved": original_route["total_distance"] - route_result["total_distance"],
            "time_saved": original_route["total_time"] - route_result["total_time"]
        }
    
    return {
        "stops": optimized_stops,
        "total_distance": route_result["total_distance"],
        "total_time": route_result["total_time"],
        "coordinates": route_result["coordinates"],
        "segments": route_result["segments"],
        "geometry": route_result["geometry"],
        "optimized": optimize_order,
        "vehicle_type": vehicle_type,
        "savings": savings
    }


def calculate_route_alternatives(stops: List[Dict]) -> List[Dict[str, Any]]:
    """
    Generate multiple route alternatives for comparison
    """
    alternatives = []
    
    # Original order
    original_route = calculate_multi_stop_route(stops, optimize_order=False)
    alternatives.append({
        "name": "Original Order",
        "type": "original",
        **original_route
    })
    
    # Optimized for time
    if len(stops) > 2:
        time_optimized = calculate_multi_stop_route(stops, optimize_order=True)
        alternatives.append({
            "name": "Fastest Route",
            "type": "time_optimized",
            **time_optimized
        })
    
    # Reverse order (sometimes useful)
    if len(stops) > 2:
        reversed_stops = stops[:1] + list(reversed(stops[1:-1])) + stops[-1:]
        reverse_route = calculate_multi_stop_route(reversed_stops, optimize_order=False)
        alternatives.append({
            "name": "Reverse Order",
            "type": "reverse",
            **reverse_route
        })
    
    return alternatives
