import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from './theme/ThemeProvider';

// Components
import Navbar from './components/Navbar';
import Dashboard from './pages/Dashboard';
import EnhancedFindRoute from './pages/EnhancedFindRoute';
import History from './pages/History';
import About from './pages/About';
import Login from './pages/Login';
import Register from './pages/Register';
import Landing from './pages/Landing';
import { useAuth } from './contexts/AuthContext';



function PrivateRoute({ children }) {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? children : <Navigate to="/login" />;
}

function App() {
  const { isAuthenticated } = useAuth();
  return (
    <ThemeProvider>
      <CssBaseline />
      <Navbar />
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route
          path="/"
          element={isAuthenticated ? <Dashboard /> : <Landing />}
        />
        <Route
          path="/find-route"
          element={
            <PrivateRoute>
              <EnhancedFindRoute />
            </PrivateRoute>
          }
        />
        <Route
          path="/enhanced-route"
          element={
            <PrivateRoute>
              <EnhancedFindRoute />
            </PrivateRoute>
          }
        />
        <Route
          path="/history"
          element={
            <PrivateRoute>
              <History />
            </PrivateRoute>
          }
        />
        <Route path="/about" element={<About />} />
      </Routes>
    </ThemeProvider>
  );
}

export default App;
