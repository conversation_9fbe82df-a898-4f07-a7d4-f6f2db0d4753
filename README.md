# Optimal Route Finder for Dehradun City

A route finding application for Dehradun city featuring a FastAPI backend and React frontend.

## Features

### Core Features
- **User Authentication**: Secure login and registration system
- **Interactive Route Planning**: Find optimal routes between Dehradun landmarks
- **Multiple Route Options**: Compare different route suggestions with distances and durations
- **Real-time Weather Integration**: Get current weather conditions for your route
- **Traffic Information**: View traffic conditions and choose optimal timing
- **Vehicle Type Selection**: Optimize routes for car, bike, or walking
- **Route History**: Track and revisit your previous routes
- **Turn-by-turn Directions**: Real road paths with detailed navigation

### 🆕 Enhanced Map Features
- **Interactive Click-to-Pin**: Click anywhere on the map to create custom waypoints
- **Custom Location Management**: Save, edit, and delete custom locations with personalized names
- **Coordinate Display**: View precise latitude/longitude coordinates for any clicked location
- **Advanced Location Search**: Search for specific types of places beyond predefined landmarks
- **Category-based Search**: Find restaurants, hospitals, schools, hotels, gas stations, shopping centers, and more
- **Real-time Geocoding**: Integration with OpenStreetMap Nominatim for accurate location data
- **Search Autocomplete**: Smart suggestions as you type location names
- **Quick Category Filters**: One-click access to popular place categories
- **Enhanced Map Interactions**: Right-click context menus and intuitive pin management
- **Multiple Marker Types**: Different colored markers for landmarks, custom pins, and search results

## Technology Stack

- **Frontend**: React.js with Leaflet for maps
- **Backend**: FastAPI (Python)
- **Routing**: OpenStreetMap data via OSRM

## Screenshots

<!-- Add screenshots here when available -->

## Setup & Installation

### Windows Setup (Recommended)

#### Prerequisites
- **Python 3.8+** - Download from [python.org](https://python.org)
- **Node.js 16+** - Download from [nodejs.org](https://nodejs.org)

#### Quick Setup
1. **Run the automated setup script:**
   ```powershell
   powershell -ExecutionPolicy Bypass -File setup_windows.ps1
   ```

2. **Start the application:**
   - Double-click `start_backend.bat` to start the backend server
   - Double-click `start_frontend.bat` to start the frontend server
   - Open http://localhost:3000 in your browser

#### Manual Setup

##### Backend Setup
```powershell
cd backend
python -m venv venv
.\venv\Scripts\Activate.ps1
pip install -r requirements.txt
python run_server.py
```

##### Frontend Setup
```powershell
cd frontend
npm install
npm start
```

### Linux/Mac Setup

#### Backend
```bash
cd backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --port 8000
```

#### Frontend
```bash
cd frontend
npm install
npm start
```

### Access Points
- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## How to Use the Application

### 1. User Registration
1. Open http://localhost:3000 in your browser
2. Click "Register" in the navigation
3. Fill in the registration form:
   ```
   Username: your_username (letters, numbers, underscores only)
   Email: <EMAIL>
   Password: YourSecurePassword123!
   Confirm Password: YourSecurePassword123!
   ```
4. Click "Register" to create your account

### 2. Login
1. Click "Login" in the navigation
2. Enter your username and password
3. Click "Login" to access the application

### 3. Finding Routes
1. Navigate to "Find Route" section
2. Select your starting location from the dropdown (e.g., "Clock Tower")
3. Select your destination (e.g., "Rajpur Road")
4. Choose your vehicle type: Car, Bike, or Walk
5. Click "Find Route" to get:
   - Multiple route options with distances and durations
   - Real-time weather conditions
   - Traffic information
   - Interactive map with turn-by-turn directions

### 4. Route History
- View your previous routes in the "History" section
- Track your most frequently used locations
- Access saved route details

### 5. Enhanced Map Features

#### Interactive Click-to-Pin
1. **Click anywhere on the map** to create a custom waypoint
2. **Enter a custom name** for your location in the popup dialog
3. **Save the pin** to use it as a start or end point for routes
4. **Edit or delete pins** using the popup controls

#### Advanced Location Search
1. **Use the search bar** at the top of the route settings
2. **Type to search** for specific places (e.g., "restaurants near Clock Tower")
3. **Select categories** using the filter button or quick category buttons:
   - 🍽️ Restaurants & Food
   - 🏥 Hospitals & Medical
   - 🏫 Schools & Education
   - 🏨 Hotels & Accommodation
   - ⛽ Gas Stations
   - 🛒 Shopping & Markets
   - 🏛️ Government & Public Services
   - 🏦 Banks & ATMs
   - 💊 Pharmacies
   - 🕌 Places of Worship

#### Using Search Results
1. **Click on search result markers** (blue pins) to see details
2. **Use "Use as waypoint" button** to add them to your route
3. **Combine with custom pins** for complex multi-stop routes

### Example Test Users
You can register with these example credentials:

**User 1:**
```
Username: demo_user
Email: <EMAIL>
Password: DemoPass123!
```

**User 2:**
```
Username: test_driver
Email: <EMAIL>
Password: TestRoute456!
```

## API Documentation

The API documentation is available at http://localhost:8000/docs when the backend server is running.

## Troubleshooting (Windows)

### Common Issues and Solutions

#### 1. PowerShell Execution Policy Error
If you get an execution policy error when running scripts:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 2. Python Virtual Environment Issues
If the virtual environment doesn't activate properly:
```powershell
cd backend
python -m venv venv --clear
.\venv\Scripts\pip.exe install -r requirements.txt
```

#### 3. Node.js/NPM Issues
If npm install fails:
```powershell
cd frontend
npm cache clean --force
npm install
```

#### 4. Port Already in Use
If ports 3000 or 8000 are already in use:
- **Backend**: Edit `backend/run_server.py` and change port from 8000 to 8001
- **Frontend**: The React app will automatically suggest an alternative port

#### 5. API Connection Issues
If the frontend can't connect to the backend:
1. Ensure the backend is running on http://localhost:8000
2. Check that `frontend/.env` contains: `REACT_APP_API_URL=http://localhost:8000`
3. Restart both servers

### Windows-Specific File Paths
The application now uses proper Windows file paths and should work correctly with:
- Backslashes in file paths
- Windows-style environment variables
- PowerShell command syntax

## Future Improvements

- Real-time traffic data integration
- User profiles and saved routes
- Mobile application version

## Contributors

- Alok Nawani

## License

MIT License