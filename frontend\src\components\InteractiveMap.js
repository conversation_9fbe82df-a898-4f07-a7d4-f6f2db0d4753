import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import L from 'leaflet';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  Tooltip,
  Chip,
  Stack
} from '@mui/material';
import {
  MyLocation as MyLocationIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Place as PlaceIcon,
  Flag as FlagIcon,
  Restaurant as RestaurantIcon,
  LocalGasStation as GasIcon,
  LocalHospital as HospitalIcon,
  Add as AddIcon,
  School as SchoolIcon,
  Hotel as HotelIcon,
  ShoppingCart as ShoppingIcon
} from '@mui/icons-material';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icons
const customPinIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const searchResultIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-blue.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Enhanced marker icons for stops and amenities
const startStopIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-green.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [30, 49],
  iconAnchor: [15, 49],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const endStopIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [30, 49],
  iconAnchor: [15, 49],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const waypointIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-orange.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const amenityIcon = new L.Icon({
  iconUrl: 'https://cdn.jsdelivr.net/gh/pointhi/leaflet-color-markers@master/img/marker-icon-2x-violet.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [20, 33],
  iconAnchor: [10, 33],
  popupAnchor: [1, -34],
  shadowSize: [33, 33]
});

// Helper functions
const getStopIcon = (stopType) => {
  switch (stopType) {
    case 'start': return startStopIcon;
    case 'end': return endStopIcon;
    case 'amenity': return amenityIcon;
    default: return waypointIcon;
  }
};

const getAmenityIcon = (category) => {
  // Could be enhanced with category-specific icons
  return amenityIcon;
};

const formatStopNumber = (index, stopType) => {
  if (stopType === 'start') return 'S';
  if (stopType === 'end') return 'E';
  return (index + 1).toString();
};

// Component to handle map clicks
function MapClickHandler({ onMapClick, onRightClick }) {
  useMapEvents({
    click: (e) => {
      if (onMapClick) {
        onMapClick(e.latlng);
      }
    },
    contextmenu: (e) => {
      if (onRightClick) {
        onRightClick(e.latlng, e.originalEvent);
      }
    }
  });
  return null;
}

// Dehradun city bounds for map restrictions (more precise)
const DEHRADUN_BOUNDS = [
  [30.25, 77.95], // Southwest corner
  [30.45, 78.15]  // Northeast corner
];

// Optimal Dehradun center coordinates
const DEHRADUN_CENTER = [30.3165, 78.0322];

const InteractiveMap = ({
  center = DEHRADUN_CENTER,
  zoom = 12, // Better zoom level for Dehradun city view
  customPins = [],
  searchResults = [],
  routePaths = [],
  landmarks = [],
  stops = [],
  amenities = [],
  onPinAdd,
  onPinUpdate,
  onPinDelete,
  onLocationSelect,
  onStopAdd,
  onStopEdit,
  onStopDelete,
  onStopSelect,
  onAmenityAdd,
  selectedStopId = null,
  height = '500px'
}) => {
  const [showPinDialog, setShowPinDialog] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [pinName, setPinName] = useState('');
  const [editingPin, setEditingPin] = useState(null);
  const mapRef = useRef();

  // Fix map display issues by invalidating size when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.invalidateSize();
      }
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Invalidate map size when height changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.invalidateSize();
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [height]);

  const handleMapClick = (latlng) => {
    setSelectedLocation(latlng);
    setPinName('');
    setEditingPin(null);
    setShowPinDialog(true);
  };

  const handleRightClick = (latlng, event) => {
    event.preventDefault();
    // Could implement context menu here
    console.log('Right click at:', latlng);
  };

  const handleSavePin = () => {
    if (selectedLocation) {
      const newPin = {
        id: editingPin ? editingPin.id : Date.now(),
        lat: selectedLocation.lat,
        lng: selectedLocation.lng,
        name: pinName || `Custom Location ${customPins.length + 1}`,
        type: 'custom',
        custom: true
      };

      if (editingPin) {
        onPinUpdate && onPinUpdate(newPin);
      } else {
        onPinAdd && onPinAdd(newPin);
      }
    }
    setShowPinDialog(false);
    setSelectedLocation(null);
    setPinName('');
    setEditingPin(null);
  };

  const handleEditPin = (pin) => {
    setSelectedLocation({ lat: pin.lat, lng: pin.lng });
    setPinName(pin.name);
    setEditingPin(pin);
    setShowPinDialog(true);
  };

  const handleDeletePin = (pin) => {
    onPinDelete && onPinDelete(pin);
  };

  const handleLocationSelect = (location) => {
    onLocationSelect && onLocationSelect(location);
  };

  return (
    <Box sx={{ height, width: '100%', position: 'relative' }}>
      <MapContainer
        center={center}
        zoom={zoom}
        style={{
          height: '100%',
          width: '100%',
          minHeight: '400px',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
        ref={mapRef}
        maxBounds={DEHRADUN_BOUNDS}
        maxBoundsViscosity={0.8}
        minZoom={10}
        maxZoom={19}
        zoomControl={true}
        scrollWheelZoom={true}
        doubleClickZoom={true}
        touchZoom={true}
        dragging={true}
        preferCanvas={true}
        attributionControl={true}
        whenReady={() => {
          // Ensure map renders properly when ready
          setTimeout(() => {
            if (mapRef.current) {
              mapRef.current.invalidateSize();
            }
          }, 100);
        }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maxZoom={19}
          tileSize={256}
          zoomOffset={0}
          updateWhenIdle={false}
          updateWhenZooming={true}
          keepBuffer={3}
          maxNativeZoom={19}
          errorTileUrl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        />

        <MapClickHandler
          onMapClick={handleMapClick}
          onRightClick={handleRightClick}
        />

        {/* Predefined landmarks */}
        {landmarks.map((landmark, index) => (
          <Marker
            key={`landmark-${index}`}
            position={[landmark.lat, landmark.lng]}
          >
            <Popup>
              <Box>
                <Typography variant="subtitle2">{landmark.name}</Typography>
                <Typography variant="caption" color="textSecondary">
                  {landmark.type} • {landmark.traffic_zone} traffic
                </Typography>
                <br />
                <Button
                  size="small"
                  onClick={() => handleLocationSelect(landmark)}
                  startIcon={<PlaceIcon />}
                >
                  Use as waypoint
                </Button>
              </Box>
            </Popup>
          </Marker>
        ))}

        {/* Custom pins */}
        {customPins.map((pin) => (
          <Marker
            key={`custom-${pin.id}`}
            position={[pin.lat, pin.lng]}
            icon={customPinIcon}
          >
            <Popup>
              <Box>
                <Typography variant="subtitle2">{pin.name}</Typography>
                <Typography variant="caption" color="textSecondary">
                  Custom Location
                </Typography>
                <Typography variant="caption" display="block">
                  {pin.lat.toFixed(4)}, {pin.lng.toFixed(4)}
                </Typography>
                <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    onClick={() => handleLocationSelect(pin)}
                    startIcon={<PlaceIcon />}
                  >
                    Use
                  </Button>
                  <IconButton
                    size="small"
                    onClick={() => handleEditPin(pin)}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeletePin(pin)}
                    color="error"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </Popup>
          </Marker>
        ))}

        {/* Search results */}
        {searchResults.map((result, index) => (
          <Marker
            key={`search-${index}`}
            position={[result.lat, result.lng]}
            icon={searchResultIcon}
          >
            <Popup>
              <Box>
                <Typography variant="subtitle2">{result.name}</Typography>
                <Typography variant="caption" color="textSecondary">
                  {result.category} • {result.type}
                </Typography>
                <Typography variant="caption" display="block">
                  {result.address}
                </Typography>
                <Button
                  size="small"
                  sx={{ mt: 1 }}
                  onClick={() => handleLocationSelect(result)}
                  startIcon={<PlaceIcon />}
                >
                  Use as waypoint
                </Button>
              </Box>
            </Popup>
          </Marker>
        ))}

        {/* Route stops */}
        {stops.map((stop, index) => (
          <Marker
            key={`stop-${stop.id}`}
            position={[stop.lat, stop.lng]}
            icon={getStopIcon(stop.stop_type)}
          >
            <Popup>
              <Box>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                  {stop.stop_type === 'start' && <FlagIcon color="success" />}
                  {stop.stop_type === 'end' && <FlagIcon color="error" />}
                  {stop.stop_type === 'waypoint' && <PlaceIcon color="primary" />}
                  {stop.stop_type === 'amenity' && <RestaurantIcon color="secondary" />}
                  <Typography variant="subtitle2">{stop.name}</Typography>
                  <Chip
                    label={formatStopNumber(index, stop.stop_type)}
                    size="small"
                    color={stop.stop_type === 'start' ? 'success' : stop.stop_type === 'end' ? 'error' : 'primary'}
                  />
                </Stack>
                <Typography variant="caption" color="textSecondary" display="block">
                  {stop.stop_type === 'start' ? 'Start Point' :
                   stop.stop_type === 'end' ? 'End Point' :
                   stop.stop_type === 'amenity' ? `Amenity • ${stop.category}` : 'Waypoint'}
                </Typography>
                <Typography variant="caption" display="block">
                  {stop.lat.toFixed(4)}, {stop.lng.toFixed(4)}
                </Typography>
                <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button
                    size="small"
                    onClick={() => onStopSelect && onStopSelect(stop)}
                    variant={selectedStopId === stop.id ? 'contained' : 'outlined'}
                    startIcon={<PlaceIcon />}
                  >
                    Select
                  </Button>
                  <IconButton
                    size="small"
                    onClick={() => onStopEdit && onStopEdit(stop)}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => onStopDelete && onStopDelete(stop.id)}
                    color="error"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </Popup>
          </Marker>
        ))}

        {/* Amenities */}
        {amenities.map((amenity, index) => (
          <Marker
            key={`amenity-${amenity.id}`}
            position={[amenity.lat, amenity.lng]}
            icon={getAmenityIcon(amenity.category)}
          >
            <Popup>
              <Box>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                  {amenity.category === 'restaurant' && <RestaurantIcon color="primary" />}
                  {amenity.category === 'gas_station' && <GasIcon color="primary" />}
                  {amenity.category === 'hospital' && <HospitalIcon color="primary" />}
                  {amenity.category === 'school' && <SchoolIcon color="primary" />}
                  {amenity.category === 'hotel' && <HotelIcon color="primary" />}
                  {amenity.category === 'shopping' && <ShoppingIcon color="primary" />}
                  <Typography variant="subtitle2">{amenity.name}</Typography>
                </Stack>
                <Typography variant="caption" color="textSecondary" display="block">
                  {amenity.category} • {(amenity.distance_from_route / 1000).toFixed(1)}km from route
                </Typography>
                {amenity.estimated_detour_time > 0 && (
                  <Typography variant="caption" color="warning.main" display="block">
                    +{Math.round(amenity.estimated_detour_time / 60)} min detour
                  </Typography>
                )}
                <Button
                  size="small"
                  sx={{ mt: 1 }}
                  onClick={() => onAmenityAdd && onAmenityAdd(amenity)}
                  startIcon={<AddIcon />}
                  variant="outlined"
                >
                  Add to Route
                </Button>
              </Box>
            </Popup>
          </Marker>
        ))}

        {/* Route paths */}
        {routePaths.map((path, index) => (
          <Polyline
            key={`route-${index}`}
            positions={path.coordinates}
            color={path.color || '#3388ff'}
            weight={path.weight || 4}
            opacity={path.opacity || 0.7}
          />
        ))}
      </MapContainer>

      {/* Pin creation/editing dialog */}
      <Dialog open={showPinDialog} onClose={() => setShowPinDialog(false)}>
        <DialogTitle>
          {editingPin ? 'Edit Custom Location' : 'Add Custom Location'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Coordinates: {selectedLocation?.lat.toFixed(4)}, {selectedLocation?.lng.toFixed(4)}
            </Typography>
            <TextField
              autoFocus
              fullWidth
              label="Location Name"
              value={pinName}
              onChange={(e) => setPinName(e.target.value)}
              placeholder="Enter a name for this location"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPinDialog(false)}>Cancel</Button>
          <Button onClick={handleSavePin} variant="contained" startIcon={<SaveIcon />}>
            {editingPin ? 'Update' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InteractiveMap;
