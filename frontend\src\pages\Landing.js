import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Paper, Typography, Button, Box, Fade, Stack } from '@mui/material';
import DeliveryDiningIcon from '@mui/icons-material/DeliveryDining';
import MapIcon from '@mui/icons-material/Map';
import PlaceIcon from '@mui/icons-material/Place';
import RouteIcon from '@mui/icons-material/Route';

const features = [
  { icon: <MapIcon sx={{ fontSize: 32, color: '#fff' }} />, text: 'Live Dehradun Map' },
  { icon: <PlaceIcon sx={{ fontSize: 32, color: '#fff' }} />, text: 'Landmark-based Stops' },
  { icon: <RouteIcon sx={{ fontSize: 32, color: '#fff' }} />, text: 'Multi-stop Route Optimization' },
  { icon: <DeliveryDiningIcon sx={{ fontSize: 32, color: '#fff' }} />, text: 'For Delivery, Logistics & More' },
];

const Landing = () => {
  const navigate = useNavigate();
  return (
    <Box sx={{
      minHeight: '100vh',
      background: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`,
      backgroundAttachment: 'fixed',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      overflow: 'hidden',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        opacity: 0.3,
      }
    }}>
      <Fade in timeout={1200}>
        <Paper elevation={8} sx={{
          p: { xs: 4, sm: 6 },
          borderRadius: 4,
          textAlign: 'center',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          color: '#2D3748',
          minWidth: { xs: '90vw', sm: 500 },
          maxWidth: 650,
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          position: 'relative',
          zIndex: 1,
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
            <DeliveryDiningIcon sx={{
              fontSize: 80,
              background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              animation: 'float 3s ease-in-out infinite'
            }} />
          </Box>
          <Typography variant="h1" fontWeight={700} gutterBottom className="gradient-text" sx={{
            letterSpacing: '-0.02em',
            fontFamily: 'Poppins, sans-serif',
            mb: 2
          }}>
            Optimal Delivery Route Finder
          </Typography>
          <Typography variant="h5" sx={{ mb: 2, opacity: 0.95, fontWeight: 400 }}>
            The smartest way to plan and optimize your delivery routes in Dehradun.
          </Typography>
          <Typography variant="subtitle1" sx={{ mb: 4, opacity: 0.85 }}>
            Fast, reliable, and landmark-based multi-stop navigation for couriers, businesses, and anyone on the move.
          </Typography>
          <Stack direction="row" spacing={3} justifyContent="center" sx={{ mb: 4 }}>
            <Button variant="contained" color="secondary" size="large" sx={{ borderRadius: 3, px: 5, fontWeight: 700, fontSize: '1.1rem', boxShadow: 3 }} onClick={() => navigate('/login')}>
              Sign In
            </Button>
            <Button variant="outlined" color="inherit" size="large" sx={{ borderRadius: 3, px: 5, fontWeight: 700, fontSize: '1.1rem', borderColor: 'white', color: 'white', '&:hover': { background: 'rgba(255,255,255,0.1)' } }} onClick={() => navigate('/register')}>
              Sign Up
            </Button>
          </Stack>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, flexWrap: 'wrap', mb: 2 }}>
            {features.map((f, i) => (
              <Box key={i} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mx: 1 }}>
                {f.icon}
                <Typography variant="body2" sx={{ color: 'white', mt: 1, fontWeight: 500 }}>{f.text}</Typography>
              </Box>
            ))}
          </Box>
          <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)', mt: 2, fontSize: 14 }}>
            &copy; {new Date().getFullYear()} Dehradun Optimal Delivery Route Finder
          </Typography>
        </Paper>
      </Fade>
      <style>{`
        @keyframes bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-12px); }
        }
      `}</style>
    </Box>
  );
};

export default Landing;
