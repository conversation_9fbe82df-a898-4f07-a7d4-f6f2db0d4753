import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Paper, Typography, Button, Box, Fade, Stack } from '@mui/material';
import DeliveryDiningIcon from '@mui/icons-material/DeliveryDining';
import MapIcon from '@mui/icons-material/Map';
import PlaceIcon from '@mui/icons-material/Place';
import RouteIcon from '@mui/icons-material/Route';

const features = [
  { icon: <MapIcon sx={{ fontSize: 32, color: '#6366f1' }} />, text: 'Live Dehradun Map' },
  { icon: <PlaceIcon sx={{ fontSize: 32, color: '#10b981' }} />, text: 'Landmark-based Stops' },
  { icon: <RouteIcon sx={{ fontSize: 32, color: '#8b5cf6' }} />, text: 'Multi-stop Optimization' },
  { icon: <DeliveryDiningIcon sx={{ fontSize: 32, color: '#06b6d4' }} />, text: 'Delivery & Logistics' },
];

const Landing = () => {
  const navigate = useNavigate();
  return (
    <Box sx={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      overflow: 'hidden',
      px: 2,
    }}>
      <Fade in timeout={1200}>
        <Paper elevation={0} sx={{
          p: { xs: 6, sm: 8 },
          borderRadius: 3,
          textAlign: 'center',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          color: '#1e293b',
          maxWidth: 600,
          width: '100%',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          border: '1px solid rgba(255, 255, 255, 0.3)',
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
            <DeliveryDiningIcon sx={{
              fontSize: 72,
              color: '#6366f1',
            }} />
          </Box>
          <Typography variant="h3" fontWeight={700} gutterBottom sx={{
            color: '#1e293b',
            mb: 3,
            fontSize: { xs: '2rem', sm: '2.5rem' }
          }}>
            Dehradun Route Finder
          </Typography>
          <Typography variant="h6" sx={{ mb: 2, color: '#64748b', fontWeight: 400 }}>
            Smart route planning and optimization for Dehradun city
          </Typography>
          <Typography variant="body1" sx={{ mb: 6, color: '#64748b' }}>
            Fast, reliable, and landmark-based multi-stop navigation for couriers, businesses, and anyone on the move.
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" sx={{ mb: 6 }}>
            <Button
              variant="contained"
              size="large"
              sx={{
                borderRadius: 2,
                px: 4,
                py: 1.5,
                fontWeight: 600,
                fontSize: '1rem',
                textTransform: 'none'
              }}
              onClick={() => navigate('/login')}
            >
              Get Started
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{
                borderRadius: 2,
                px: 4,
                py: 1.5,
                fontWeight: 600,
                fontSize: '1rem',
                textTransform: 'none',
                borderColor: '#6366f1',
                color: '#6366f1',
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.04)',
                  borderColor: '#6366f1'
                }
              }}
              onClick={() => navigate('/register')}
            >
              Sign Up
            </Button>
          </Stack>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4, flexWrap: 'wrap', mb: 4 }}>
            {features.map((f, i) => (
              <Box key={i} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mx: 1 }}>
                {f.icon}
                <Typography variant="body2" sx={{ color: '#64748b', mt: 1, fontWeight: 500, textAlign: 'center' }}>
                  {f.text}
                </Typography>
              </Box>
            ))}
          </Box>
          <Typography variant="caption" sx={{ color: '#94a3b8', mt: 2, fontSize: 14 }}>
            &copy; {new Date().getFullYear()} Dehradun Route Finder
          </Typography>
        </Paper>
      </Fade>
      <style>{`
        @keyframes bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-12px); }
        }
      `}</style>
    </Box>
  );
};

export default Landing;
